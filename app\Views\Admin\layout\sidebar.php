
<aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <a href="<?= base_url(); ?>" class="brand-link">
      <img src="<?= base_url("assets/")?>dist/img/citem_logo_2023BG.jpg" alt="AdminLTE Logo" class="brand-image img-circle elevation-4" style="opacity: 1">
      <span class="brand-text font-weight-light">&nbsp;PMS</span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
      <!-- Sidebar user panel (optional) -->
      <div class="user-panel mt-3 pb-3 mb-3 d-flex">
        <div class="image">
          <img src="<?= base_url("assets/")?>dist/img/user2-160x160.jpg" class="img-circle elevation-2" alt="User Image">
        </div>
        <div class="info">
          <a href="#" class="d-block"><?= session()->get('username') ?? 'User'; ?></a>
        </div>
      </div>

      <!-- Sidebar Menu -->
      <nav class="mt-2">
        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
          <!-- Add icons to the links using the .nav-icon class
               with font-awesome or any other icon font library -->

          <?php
          // Try to render dynamic menus first
          $dynamicMenus = renderSidebarMenu();
          if (!empty(trim($dynamicMenus))): ?>
            <?= $dynamicMenus ?>
          <?php else: ?>
            <!-- Fallback static menus when no dynamic menus are available -->
            <li class="nav-item">
              <a href="<?= base_url('admin/dashboard') ?>" class="nav-link <?= (uri_string() == 'admin/dashboard') ? 'active' : '' ?>">
                <i class="nav-icon fas fa-tachometer-alt"></i>
                <p>Dashboard</p>
              </a>
            </li>

            <?php if (hasPermission('manage_users')): ?>
            <li class="nav-item">
              <a href="#" class="nav-link">
                <i class="nav-icon fas fa-users"></i>
                <p>
                  User Management
                  <i class="fas fa-angle-left right"></i>
                </p>
              </a>
              <ul class="nav nav-treeview">
                <li class="nav-item">
                  <a href="<?= base_url('admin/users') ?>" class="nav-link <?= (strpos(uri_string(), 'admin/users') === 0) ? 'active' : '' ?>">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Manage Users</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="<?= base_url('admin/users/create') ?>" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Add User</p>
                  </a>
                </li>
              </ul>
            </li>
            <?php endif; ?>

            <?php if (hasPermission('role.manage')): ?>
            <li class="nav-item">
              <a href="#" class="nav-link">
                <i class="nav-icon fas fa-user-shield"></i>
                <p>
                  Role Management
                  <i class="fas fa-angle-left right"></i>
                </p>
              </a>
              <ul class="nav nav-treeview">
                <li class="nav-item">
                  <a href="<?= base_url('admin/roles') ?>" class="nav-link <?= (strpos(uri_string(), 'admin/roles') === 0) ? 'active' : '' ?>">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Manage Roles</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="<?= base_url('admin/roles/create') ?>" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Add Role</p>
                  </a>
                </li>
              </ul>
            </li>
            <?php endif; ?>

            <?php if (hasPermission('menu.manage')): ?>
            <li class="nav-item">
              <a href="#" class="nav-link">
                <i class="nav-icon fas fa-cogs"></i>
                <p>
                  System Settings
                  <i class="fas fa-angle-left right"></i>
                </p>
              </a>
              <ul class="nav nav-treeview">
                <li class="nav-item">
                  <a href="<?= base_url('admin/menu-management') ?>" class="nav-link <?= (strpos(uri_string(), 'admin/menu-management') === 0) ? 'active' : '' ?>">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Menu Management</p>
                  </a>
                </li>
              </ul>
            </li>
            <?php endif; ?>
          <?php endif; ?>

        </ul>
      </nav>
      <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
</aside>
