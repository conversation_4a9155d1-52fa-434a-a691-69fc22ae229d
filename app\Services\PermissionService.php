<?php

namespace App\Services;

use Modules\UserManagement\Models\UserModel; 
use Modules\RoleManagement\Models\RoleModel;
use Modules\RoleManagement\Models\PermissionModel;

/**
 * Class PermissionService
 * Provides reusable methods for fetching permission data.
 */
class PermissionService
{
    protected RoleModel $roleModel;
    protected PermissionModel $permissionModel;
    protected UserModel $userModel;

    public function __construct()
    {
        $this->roleModel = new RoleModel();
        $this->permissionModel = new PermissionModel();
        $this->userModel = new UserModel();
    }

    /**
     * Fetches all permission IDs assigned to a specific role.
     *
     * @param int $roleId The ID of the role.
     * @return array An array of permission IDs.
     */
    public function getPermissionsByRoleId(int $roleId): array
    {
        $result = $this->roleModel->db->table('role_permissions')
                                    ->select('permission_id')
                                    ->where('role_id', $roleId)
                                    ->get()
                                    ->getResultArray();

        return array_column($result, 'permission_id');
    }

    /**
     * Fetches all permission names (slugs) for a given role ID.
     *
     * @param int $roleId The ID of the role.
     * @return array An array of permission names.
     */
    public function getPermissionNamesByRoleId(int $roleId): array
    {
        $permissionIds = $this->getPermissionsByRoleId($roleId);

        if (empty($permissionIds)) {
            return [];
        }

        $permissions = $this->permissionModel->select('name')
                                             ->whereIn('id', $permissionIds)
                                             ->findAll();

        return array_column($permissions, 'name');
    }

    /**
     * Fetches all permission names (slugs) for a specific user ID.
     * This combines getting the user's role and then its permissions.
     *
     * @param int $userId The ID of the user.
     * @return array An array of permission names.
     */
    public function getPermissionNamesByUserId(int $userId): array
    {
        $user = $this->userModel->select('role_id')->find($userId);

        if (!$user || !isset($user['role_id'])) {
            return [];
        }

        return $this->getPermissionNamesByRoleId($user['role_id']);
    }

    /**
     * Fetches all available permissions in the system.
     *
     * @return array An array of all permission records.
     */
    public function getAllPermissions(): array
    {
        return $this->permissionModel->findAll();
    }

    /**
     * Fetches all available permissions grouped by their category.
     *
     * @return array An associative array where keys are categories and values are arrays of permission records.
     */
    public function getAllPermissionsGroupedByCategory(): array
    {
        // Order by category for consistent grouping, then by name within each category
        $permissions = $this->permissionModel->orderBy('category', 'ASC')->orderBy('name', 'ASC')->findAll();
        $groupedPermissions = [];

        foreach ($permissions as $permission) {
            $category = $permission['category'] ?? 'Uncategorized'; // Provide a fallback
            if (!isset($groupedPermissions[$category])) {
                $groupedPermissions[$category] = [];
            }
            $groupedPermissions[$category][] = $permission;
        }

        return $groupedPermissions;
    }












}