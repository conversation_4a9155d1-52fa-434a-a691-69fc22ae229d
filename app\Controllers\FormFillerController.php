<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

use App\Libraries\PdfOverlay;
use setasign\Fpdi\Fpdi;

class FormFillerController extends BaseController
{
    public function fillForm()
    {
        $pdfOverlay = new PdfOverlay();
        
        $fieldData = [
            'full_name' => '<PERSON> Doe',
            'email'     => '<EMAIL>',
            'dob'       => '01/01/1990',
            'message'   => "Hello, this is a custom message that should wrap within a defined width. It may be multiple lines depending on length.",
        ];

        //get the template path from writable/pdftemplates
        $templatePath = WRITEPATH.'pdftemplates/sample.pdf';
        $outputPath = WRITEPATH.'pdf/filled_form.pdf';

        $result = $pdfOverlay->fillPdf($templatePath, $outputPath, $fieldData);

        if ($result === false) {
            return $this->response->setStatusCode(500)->setBody('Failed to fill the PDF form.');
        }
        else{
            return $this->response->download($result, null);
        }

    }

    public function testform(){
        return view('testform');
    }

    public function generate()
    {
        $rows = $this->request->getPost('rows');

        if (!$rows) {
            return 'No data received.';
        }

        $pdf = new Fpdi();
        $pdf->AddPage();
        $pdf->setSourceFile(WRITEPATH . 'pdftemplates/sample.pdf');
        $tplIdx = $pdf->importPage(1);
        $pdf->useTemplate($tplIdx);

        $pdf->SetFont('Helvetica', '', 10);
        $pdf->SetTextColor(0, 0, 0);

        // Position settings — adjust based on your PDF template
        $startY = 100;      // Starting Y coordinate
        $rowHeight = 5;     // Vertical spacing between rows
        $columns = [        // X positions for each column
            'particular' => 30,
            'quantity'   => 120,
            'cost'       => 160
        ];

        $currentY = $startY;

        foreach ($rows as $row) {
            // Always set the row position
            $pdf->SetXY($columns['particular'], $currentY);
            if (!empty(trim($row['particular']))) {
                $pdf->Write(0, $row['particular']);
            }

            $pdf->SetXY($columns['quantity'], $currentY);
            if (!empty(trim($row['quantity']))) {
                $pdf->Write(0, $row['quantity']);
            }

            $pdf->SetXY($columns['cost'], $currentY);
            if (!empty(trim($row['cost']))) {
                $pdf->Write(0, $row['cost']);
            }

            // Move to next row no matter what
            $currentY += $rowHeight;
        }

        $this->response->setHeader('Content-Type', 'application/pdf');
        $pdf->Output('I', 'overlay_table.pdf');
    }

    public function testtextarea(){
        return view('textarea');
    }

    public function overlayTextarea(){
        // Get raw textarea input
        $particulars = explode("\n", trim($this->request->getPost('particulars')));
        $quantities = explode("\n", trim($this->request->getPost('quantities')));
        $costs      = explode("\n", trim($this->request->getPost('costs')));

        // Normalize length (optional)
        $maxRows = max(count($particulars), count($quantities), count($costs));

        // Pad shorter columns with empty values to match row count
        $particulars = array_pad($particulars, $maxRows, '');
        $quantities  = array_pad($quantities, $maxRows, '');
        $costs       = array_pad($costs, $maxRows, '');

        $pdf = new \setasign\Fpdi\Fpdi();
        $pdf->AddPage();
        $pdf->setSourceFile(WRITEPATH . 'pdftemplates/sample.pdf');
        $tplIdx = $pdf->importPage(1);
        $pdf->useTemplate($tplIdx);

        $pdf->SetFont('Helvetica', '', 10);
        $pdf->SetTextColor(0, 0, 0);

        // Positioning settings
        $startY = 100;
        $rowHeight = 4;
        $columns = [
            'particular' => 30,
            'quantity'   => 120,
            'cost'       => 160
        ];

        for ($i = 0; $i < $maxRows; $i++) {
            $y = $startY + $i * $rowHeight;

            $pdf->SetXY($columns['particular'], $y);
            $pdf->Write(0, trim($particulars[$i]));

            $pdf->SetXY($columns['quantity'], $y);
            $pdf->Write(0, trim($quantities[$i]));

            $pdf->SetXY($columns['cost'], $y);
            $pdf->Write(0, trim($costs[$i]));
        }

        $this->response->setHeader('Content-Type', 'application/pdf');
        $pdf->Output('I', 'table_from_textarea.pdf');
    }
}
