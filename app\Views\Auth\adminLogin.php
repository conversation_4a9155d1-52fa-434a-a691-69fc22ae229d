<?= $this->extend('layouts/auth'); ?>

<?= $this->section('title'); ?>Signup<?= $this->endSection(); ?>

<?= $this->section('content'); ?>


<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>SALES| Log in</title>

  <!-- Google Font: Source Sans Pro -->
  <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback"> -->
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">
  <!-- icheck bootstrap -->
  <link rel="stylesheet" href="<?php echo base_url("assets/plugins/icheck-bootstrap/icheck-bootstrap.min.css"); ?>">
  <!-- Theme style -->
  <link rel="stylesheet" href="<?php echo base_url("assets/dist/css/adminlte.min.css"); ?>">
</head>
<body class="hold-transition login-page">
<div class="login-box">
  <!-- /.login-logo -->
  <div class="card card-outline card-primary">
    <div class="card-header text-center">
      <a href="login" class="h1"><b><span style="font-size: 33px;">Sales Monitoring System</span></b></a>
    </div>
    <div class="card-body">
      <p class="login-box-msg">ADMIN LOGIN</p>
      
      <?= form_open("admin/verify_login", ['id' => 'login-form','method'=>'POST'], []);?>
        <?= csrf_field() ?>

        <!-- display flash data message -->
        <?php if(session()->has('warning')): ?>
        <div class="alert alert-warning alert-dismissible"> 
                  <?php echo session()->get('warning'); ?>                  
                </div>

      <?php endif ?>
        <?php
            if(session()->getFlashdata('success')) { ?>
                <div class="alert alert-success alert-dismissible"> 
                  test                   
                    <?php echo session()->getFlashdata('success') ?>
                </div>
            <?php } elseif(session()->getFlashdata('failed')) { ?>
                <div class="alert alert-danger alert-dismissible">
                    <?php echo session()->getFlashdata('failed') ?>
                </div>
        <?php } ?>

        <div class="input-group mb-3 has-validation">
          <input type="text" class="form-control <?php echo isInvalid('username') ?>" name="username" placeholder="Username" value="<?php echo old('username') ?>"/>          
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-envelope"></span>
            </div>
          </div>

              <div class="invalid-feedback">
                  <?php echo show_validation_error('username',session("errors"));?>
              </div>                                

        </div>


        <div class="input-group mb-3">
          <input id="password" type="password" class="form-control <?php echo isInvalid('password') ?>" name="password" placeholder="Password" value=""/>
          <div class="input-group-append">
            <div class="input-group-text">
              <span id="eye" class="fas fa-eye"></span>
            </div>
          </div>

                <div class="invalid-feedback">
                    <?php echo show_validation_error('password',session("errors"));?>
                </div>                                

        </div>

        <div class="row">

          <!-- /.col -->
          <div class="col-4">
            <button type="submit" name="submit" value="1" class="btn btn-primary btn-block">LOGIN</button>
          </div>
          <!-- /.col -->
        </div>
      </form>
     <br>
    </div>
    <!-- /.card-body -->
  </div>
  <!-- /.card -->
</div>
<!-- /.login-box -->

<script>
const passwordInput = document.querySelector("#password")
const eye = document.querySelector("#eye")
console.log(passwordInput);
console.log(eye);
function passwordToggle(){

}

eye.addEventListener("click", function(){
  this.classList.toggle("fa-eye-slash")
  const type = passwordInput.getAttribute("type") === "password" ? "text" : "password"
  passwordInput.setAttribute("type", type)
})

$(document).ready(function(){
  $('#eventSector').change(function(){ 
     if (document.getElementById('eventSector').value === "") {
      //alert(myVar1);
      $('#eventProj').prop('disabled', true);
     }
     else {
      $('#eventProj').prop('disabled', false);
     }
  });
});

</script>

</body>
</html>








<?= $this->endSection(); ?>