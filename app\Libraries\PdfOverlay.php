<?php
namespace App\Libraries;

use setasign\Fpdi\Fpdi;

class PdfOverlay
{
     /**
     * Field position map:
     * field_name => [x, y, type ('single' or 'multi'), width (for multi), height (line height)]
     */
    private array $fieldPositions = [
        'full_name' => [50, 60, 'single'],
        'email'     => [50, 70, 'single'],
        'dob'       => [50, 80, 'single'],
        'message'   => [50, 100, 'multi', 100, 6], // width=100mm, line height=6mm
    ];

    /**
     * Overlay data onto an existing PDF
     *
     * @param string $templatePath Full path to the PDF template
     * @param string $outputPath   Full path where to save the new PDF
     * @param array  $fieldData    Field values: ['field_name' => 'value']
     * @return string|false        Output path or false on failure
     */
    public function fillPdf(string $templatePath, string $outputPath, array $fieldData)
    {
        if (!file_exists($templatePath)) {
            return false;
        }

        $pdf = new Fpdi();
        $pdf->AddPage();
        $pdf->setSourceFile($templatePath);
        $tpl = $pdf->importPage(1);
        $pdf->useTemplate($tpl);

        $pdf->SetFont('Helvetica', '', 11);
        $pdf->SetTextColor(0, 0, 0);

        foreach ($fieldData as $field => $value) {
            if (isset($this->fieldPositions[$field])) {
                $config = $this->fieldPositions[$field];
                $x = $config[0];
                $y = $config[1];
                $type = $config[2];

                $pdf->SetXY($x, $y);

                if ($type === 'single') {
                    $pdf->Write(10, $value);
                } elseif ($type === 'multi') {
                    $width = $config[3] ?? 100;
                    $lineHeight = $config[4] ?? 6;
                    $pdf->MultiCell($width, $lineHeight, $value, 0, 'L');
                }
            }
        }

        $pdf->Output('F', $outputPath);
        return $outputPath;
    }
}