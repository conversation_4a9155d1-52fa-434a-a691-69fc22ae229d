<?php

namespace App\Models;

use CodeIgniter\Model;

class ApprovalHierarchyModel extends Model
{
    protected $DBGroup = 'default';
    protected $table            = 'approval_hierarchy';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['name', 'description', 'level'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'name'        => 'required|alpha_numeric_space|min_length[3]|max_length[255]|is_unique[approval_hierarchy.name,id,{id}]',
        'description' => 'permit_empty|max_length[500]',
        'level'       => 'required|integer|greater_than_equal_to[1]',
    ];
    protected $validationMessages = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;
}