<?= $this->extend('layouts/auth'); ?>

<?= $this->section('title'); ?>Forgot Password<?= $this->endSection(); ?>

<?= $this->section('content'); ?>
  <div class="login-box">
      <div class="panel-body">
        <div class="text-center">
          <h3><i class="fa fa-lock fa-4x"></i></h3>
          <h2 class="text-center">Account Recovery</h2>
          <p>Enter the email address and we'll send you a link to reset your password</p>
          <div class="panel-body">
            <?= form_open("auth/forgot_password",'id="forgotpass-form" role="form" autocomplete="off" class="form"');?>

 
 
            <?php if(!empty($errors)): ?>
            <div class="alert alert-dismissible alert-warning">
              <button type="button" class="close" data-dismiss="alert">&times;</button>
              <p class="mb-0"><?php echo $errors;?></p>
            </div>

            <?php endif; ?>
            <?php if(!empty($message)): ?>
            <div id="infoMessage" class="alert alert-dismissible alert-success">
              <button type="button" class="close" data-dismiss="alert">&times;</button>
              <?php echo $message;?>
            </div>
            <?php endif; ?>

              <div class="form-group">
                <input id="email" name="email" placeholder="email address" class="form-control <?php echo isInvalid('email') ?>"  type="text">

                <div class="invalid-feedback">
                  <?= isset($validation) ? $validation->getError('email') : '' ?>
              </div>   
              </div> 

               <div class="form-group">
                <input name="recover-submit" class="btn btn-lg btn-primary btn-block" value="Next" type="submit">
              </div>
              <input type="hidden" class="hide" name="token" id="token" value="">
            </form>
          </div>
        </div>
      </div>
  </div>


<?= $this->endSection(); ?>
