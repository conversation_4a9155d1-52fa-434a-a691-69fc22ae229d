{"version": 3, "file": "bs-stepper.js", "sources": ["../../src/js/polyfill.js", "../../src/js/util.js", "../../src/js/listeners.js", "../../src/js/index.js"], "sourcesContent": ["let matches = window.Element.prototype.matches\nlet closest = (element, selector) => element.closest(selector)\nlet WinEvent = (inType, params) => new window.Event(inType, params)\nlet createCustomEvent = (eventName, params) => {\n  const cEvent = new window.CustomEvent(eventName, params)\n\n  return cEvent\n}\n\n/* istanbul ignore next */\nfunction polyfill () {\n  if (!window.Element.prototype.matches) {\n    matches = window.Element.prototype.msMatchesSelector ||\n      window.Element.prototype.webkitMatchesSelector\n  }\n\n  if (!window.Element.prototype.closest) {\n    closest = (element, selector) => {\n      if (!document.documentElement.contains(element)) {\n        return null\n      }\n\n      do {\n        if (matches.call(element, selector)) {\n          return element\n        }\n\n        element = element.parentElement || element.parentNode\n      } while (element !== null && element.nodeType === 1)\n\n      return null\n    }\n  }\n\n  if (!window.Event || typeof window.Event !== 'function') {\n    WinEvent = (inType, params) => {\n      params = params || {}\n      const e = document.createEvent('Event')\n      e.initEvent(inType, Boolean(params.bubbles), Bo<PERSON>an(params.cancelable))\n      return e\n    }\n  }\n\n  if (typeof window.CustomEvent !== 'function') {\n    const originPreventDefault = window.Event.prototype.preventDefault\n\n    createCustomEvent = (eventName, params) => {\n      const evt = document.createEvent('CustomEvent')\n\n      params = params || { bubbles: false, cancelable: false, detail: null }\n      evt.initCustomEvent(eventName, params.bubbles, params.cancelable, params.detail)\n      evt.preventDefault = function () {\n        if (!this.cancelable) {\n          return\n        }\n\n        originPreventDefault.call(this)\n        Object.defineProperty(this, 'defaultPrevented', {\n          get: function () { return true }\n        })\n      }\n\n      return evt\n    }\n  }\n}\n\npolyfill()\n\nexport {\n  closest,\n  WinEvent,\n  createCustomEvent\n}\n", "import { WinEvent, createCustomEvent } from './polyfill'\n\nconst MILLISECONDS_MULTIPLIER = 1000\n\nconst ClassName = {\n  ACTIVE: 'active',\n  LINEAR: 'linear',\n  BLOCK: 'dstepper-block',\n  NONE: 'dstepper-none',\n  FADE: 'fade',\n  VERTICAL: 'vertical'\n}\n\nconst transitionEndEvent = 'transitionend'\nconst customProperty = 'bsStepper'\n\nconst show = (stepperNode, indexStep, options, done) => {\n  const stepper = stepperNode[customProperty]\n\n  if (stepper._steps[indexStep].classList.contains(ClassName.ACTIVE) || stepper._stepsContents[indexStep].classList.contains(ClassName.ACTIVE)) {\n    return\n  }\n\n  const showEvent = createCustomEvent('show.bs-stepper', {\n    cancelable: true,\n    detail: {\n      from: stepper._currentIndex,\n      to: indexStep,\n      indexStep\n    }\n  })\n  stepperNode.dispatchEvent(showEvent)\n\n  const activeStep = stepper._steps.filter(step => step.classList.contains(ClassName.ACTIVE))\n  const activeContent = stepper._stepsContents.filter(content => content.classList.contains(ClassName.ACTIVE))\n\n  if (showEvent.defaultPrevented) {\n    return\n  }\n\n  if (activeStep.length) {\n    activeStep[0].classList.remove(ClassName.ACTIVE)\n  }\n  if (activeContent.length) {\n    activeContent[0].classList.remove(ClassName.ACTIVE)\n\n    if (!stepperNode.classList.contains(ClassName.VERTICAL) && !stepper.options.animation) {\n      activeContent[0].classList.remove(ClassName.BLOCK)\n    }\n  }\n\n  showStep(stepperNode, stepper._steps[indexStep], stepper._steps, options)\n  showContent(stepperNode, stepper._stepsContents[indexStep], stepper._stepsContents, activeContent, done)\n}\n\nconst showStep = (stepperNode, step, stepList, options) => {\n  stepList.forEach(step => {\n    const trigger = step.querySelector(options.selectors.trigger)\n\n    trigger.setAttribute('aria-selected', 'false')\n    // if stepper is in linear mode, set disabled attribute on the trigger\n    if (stepperNode.classList.contains(ClassName.LINEAR)) {\n      trigger.setAttribute('disabled', 'disabled')\n    }\n  })\n\n  step.classList.add(ClassName.ACTIVE)\n  const currentTrigger = step.querySelector(options.selectors.trigger)\n\n  currentTrigger.setAttribute('aria-selected', 'true')\n  // if stepper is in linear mode, remove disabled attribute on current\n  if (stepperNode.classList.contains(ClassName.LINEAR)) {\n    currentTrigger.removeAttribute('disabled')\n  }\n}\n\nconst showContent = (stepperNode, content, contentList, activeContent, done) => {\n  const stepper = stepperNode[customProperty]\n  const toIndex = contentList.indexOf(content)\n  const shownEvent = createCustomEvent('shown.bs-stepper', {\n    cancelable: true,\n    detail: {\n      from: stepper._currentIndex,\n      to: toIndex,\n      indexStep: toIndex\n    }\n  })\n\n  function complete () {\n    content.classList.add(ClassName.BLOCK)\n    content.removeEventListener(transitionEndEvent, complete)\n    stepperNode.dispatchEvent(shownEvent)\n    done()\n  }\n\n  if (content.classList.contains(ClassName.FADE)) {\n    content.classList.remove(ClassName.NONE)\n    const duration = getTransitionDurationFromElement(content)\n\n    content.addEventListener(transitionEndEvent, complete)\n    if (activeContent.length) {\n      activeContent[0].classList.add(ClassName.NONE)\n    }\n\n    content.classList.add(ClassName.ACTIVE)\n    emulateTransitionEnd(content, duration)\n  } else {\n    content.classList.add(ClassName.ACTIVE)\n    content.classList.add(ClassName.BLOCK)\n    stepperNode.dispatchEvent(shownEvent)\n    done()\n  }\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let transitionDuration = window.getComputedStyle(element).transitionDuration\n  const floatTransitionDuration = parseFloat(transitionDuration)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n\n  return parseFloat(transitionDuration) * MILLISECONDS_MULTIPLIER\n}\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener () {\n    called = true\n    element.removeEventListener(transitionEndEvent, listener)\n  }\n\n  element.addEventListener(transitionEndEvent, listener)\n  window.setTimeout(() => {\n    if (!called) {\n      element.dispatchEvent(WinEvent(transitionEndEvent))\n    }\n\n    element.removeEventListener(transitionEndEvent, listener)\n  }, emulatedDuration)\n}\n\nconst detectAnimation = (contentList, options) => {\n  if (options.animation) {\n    contentList.forEach(content => {\n      content.classList.add(ClassName.FADE)\n      content.classList.add(ClassName.NONE)\n    })\n  }\n}\n\nexport {\n  show,\n  ClassName,\n  customProperty,\n  detectAnimation\n}\n", "import { closest } from './polyfill'\r\nimport { customProperty, show } from './util'\r\n\r\nconst buildClickStepLinearListener = () => function clickStepLinearListener (event) {\r\n  event.preventDefault()\r\n}\r\n\r\nconst buildClickStepNonLinearListener = options => function clickStepNonLinearListener (event) {\r\n  event.preventDefault()\r\n\r\n  const step = closest(event.target, options.selectors.steps)\r\n  const stepperNode = closest(step, options.selectors.stepper)\r\n  const stepper = stepperNode[customProperty]\r\n  const stepIndex = stepper._steps.indexOf(step)\r\n\r\n  show(stepperNode, stepIndex, options, () => {\r\n    stepper._currentIndex = stepIndex\r\n  })\r\n}\r\n\r\nexport {\r\n  buildClickStepLinearListener,\r\n  buildClickStepNonLinearListener\r\n}\r\n", "import { show, customProperty, detectAnimation, ClassName } from './util'\r\nimport { buildClickStepLinearListener, buildClickStepNonLinearListener } from './listeners'\r\n\r\nconst DEFAULT_OPTIONS = {\r\n  linear: true,\r\n  animation: false,\r\n  selectors: {\r\n    steps: '.step',\r\n    trigger: '.step-trigger',\r\n    stepper: '.bs-stepper'\r\n  }\r\n}\r\n\r\nclass Stepper {\r\n  constructor (element, _options = {}) {\r\n    this._element = element\r\n    this._currentIndex = 0\r\n    this._stepsContents = []\r\n\r\n    this.options = {\r\n      ...DEFAULT_OPTIONS,\r\n      ..._options\r\n    }\r\n\r\n    this.options.selectors = {\r\n      ...DEFAULT_OPTIONS.selectors,\r\n      ...this.options.selectors\r\n    }\r\n\r\n    if (this.options.linear) {\r\n      this._element.classList.add(ClassName.LINEAR)\r\n    }\r\n\r\n    this._steps = [].slice.call(this._element.querySelectorAll(this.options.selectors.steps))\r\n\r\n    this._steps.filter(step => step.hasAttribute('data-target'))\r\n      .forEach(step => {\r\n        this._stepsContents.push(\r\n          this._element.querySelector(step.getAttribute('data-target'))\r\n        )\r\n      })\r\n\r\n    detectAnimation(this._stepsContents, this.options)\r\n    this._setLinkListeners()\r\n    Object.defineProperty(this._element, customProperty, {\r\n      value: this,\r\n      writable: true\r\n    })\r\n\r\n    if (this._steps.length) {\r\n      show(this._element, this._currentIndex, this.options, () => {})\r\n    }\r\n  }\r\n\r\n  // Private\r\n\r\n  _setLinkListeners () {\r\n    this._steps.forEach(step => {\r\n      const trigger = step.querySelector(this.options.selectors.trigger)\r\n\r\n      if (this.options.linear) {\r\n        this._clickStepLinearListener = buildClickStepLinearListener(this.options)\r\n        trigger.addEventListener('click', this._clickStepLinearListener)\r\n      } else {\r\n        this._clickStepNonLinearListener = buildClickStepNonLinearListener(this.options)\r\n        trigger.addEventListener('click', this._clickStepNonLinearListener)\r\n      }\r\n    })\r\n  }\r\n\r\n  // Public\r\n\r\n  next () {\r\n    const nextStep = (this._currentIndex + 1) <= this._steps.length - 1 ? this._currentIndex + 1 : (this._steps.length - 1)\r\n\r\n    show(this._element, nextStep, this.options, () => {\r\n      this._currentIndex = nextStep\r\n    })\r\n  }\r\n\r\n  previous () {\r\n    const previousStep = (this._currentIndex - 1) >= 0 ? this._currentIndex - 1 : 0\r\n\r\n    show(this._element, previousStep, this.options, () => {\r\n      this._currentIndex = previousStep\r\n    })\r\n  }\r\n\r\n  to (stepNumber) {\r\n    const tempIndex = stepNumber - 1\r\n    const nextStep = tempIndex >= 0 && tempIndex < this._steps.length\r\n      ? tempIndex\r\n      : 0\r\n\r\n    show(this._element, nextStep, this.options, () => {\r\n      this._currentIndex = nextStep\r\n    })\r\n  }\r\n\r\n  reset () {\r\n    show(this._element, 0, this.options, () => {\r\n      this._currentIndex = 0\r\n    })\r\n  }\r\n\r\n  destroy () {\r\n    this._steps.forEach(step => {\r\n      const trigger = step.querySelector(this.options.selectors.trigger)\r\n\r\n      if (this.options.linear) {\r\n        trigger.removeEventListener('click', this._clickStepLinearListener)\r\n      } else {\r\n        trigger.removeEventListener('click', this._clickStepNonLinearListener)\r\n      }\r\n    })\r\n\r\n    this._element[customProperty] = undefined\r\n    this._element = undefined\r\n    this._currentIndex = undefined\r\n    this._steps = undefined\r\n    this._stepsContents = undefined\r\n    this._clickStepLinearListener = undefined\r\n    this._clickStepNonLinearListener = undefined\r\n  }\r\n}\r\n\r\nexport default Stepper\r\n"], "names": ["matches", "window", "Element", "prototype", "closest", "element", "selector", "WinEvent", "inType", "params", "Event", "createCustomEvent", "eventName", "cEvent", "CustomEvent", "polyfill", "msMatchesSelector", "webkitMatchesSelector", "document", "documentElement", "contains", "call", "parentElement", "parentNode", "nodeType", "e", "createEvent", "initEvent", "Boolean", "bubbles", "cancelable", "originPreventDefault", "preventDefault", "evt", "detail", "initCustomEvent", "Object", "defineProperty", "get", "MILLISECONDS_MULTIPLIER", "ClassName", "ACTIVE", "LINEAR", "BLOCK", "NONE", "FADE", "VERTICAL", "transitionEndEvent", "customProperty", "show", "stepperNode", "indexStep", "options", "done", "stepper", "_steps", "classList", "_stepsContents", "showEvent", "from", "_currentIndex", "to", "dispatchEvent", "activeStep", "filter", "step", "activeContent", "content", "defaultPrevented", "length", "remove", "animation", "showStep", "showContent", "stepList", "for<PERSON>ach", "trigger", "querySelector", "selectors", "setAttribute", "add", "currentTrigger", "removeAttribute", "contentList", "toIndex", "indexOf", "shownEvent", "complete", "removeEventListener", "duration", "getTransitionDurationFromElement", "addEventListener", "emulateTransitionEnd", "transitionDuration", "getComputedStyle", "floatTransitionDuration", "parseFloat", "split", "called", "durationPadding", "emulatedDuration", "listener", "setTimeout", "detectAnimation", "buildClickStepLinearListener", "clickStepLinearListener", "event", "buildClickStepNonLinearListener", "clickStepNonLinearListener", "target", "steps", "stepIndex", "DEFAULT_OPTIONS", "linear", "Stepper", "_options", "_element", "slice", "querySelectorAll", "hasAttribute", "push", "getAttribute", "_setLinkListeners", "value", "writable", "_clickStepLinearListener", "_clickStepNonLinearListener", "next", "nextStep", "previous", "previousStep", "<PERSON><PERSON><PERSON><PERSON>", "tempIndex", "reset", "destroy", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IAAIA,OAAO,GAAGC,MAAM,CAACC,OAAP,CAAeC,SAAf,CAAyBH,OAAvC;;EACA,IAAII,OAAO,GAAG,iBAACC,OAAD,EAAUC,QAAV;EAAA,SAAuBD,OAAO,CAACD,OAAR,CAAgBE,QAAhB,CAAvB;EAAA,CAAd;;EACA,IAAIC,QAAQ,GAAG,kBAACC,MAAD,EAASC,MAAT;EAAA,SAAoB,IAAIR,MAAM,CAACS,KAAX,CAAiBF,MAAjB,EAAyBC,MAAzB,CAApB;EAAA,CAAf;;EACA,IAAIE,iBAAiB,GAAG,2BAACC,SAAD,EAAYH,MAAZ,EAAuB;EAC7C,MAAMI,MAAM,GAAG,IAAIZ,MAAM,CAACa,WAAX,CAAuBF,SAAvB,EAAkCH,MAAlC,CAAf;EAEA,SAAOI,MAAP;EACD,CAJD;EAMA;;;EACA,SAASE,QAAT,GAAqB;EACnB,MAAI,CAACd,MAAM,CAACC,OAAP,CAAeC,SAAf,CAAyBH,OAA9B,EAAuC;EACrCA,IAAAA,OAAO,GAAGC,MAAM,CAACC,OAAP,CAAeC,SAAf,CAAyBa,iBAAzB,IACRf,MAAM,CAACC,OAAP,CAAeC,SAAf,CAAyBc,qBAD3B;EAED;;EAED,MAAI,CAAChB,MAAM,CAACC,OAAP,CAAeC,SAAf,CAAyBC,OAA9B,EAAuC;EACrCA,IAAAA,OAAO,GAAG,iBAACC,OAAD,EAAUC,QAAV,EAAuB;EAC/B,UAAI,CAACY,QAAQ,CAACC,eAAT,CAAyBC,QAAzB,CAAkCf,OAAlC,CAAL,EAAiD;EAC/C,eAAO,IAAP;EACD;;EAED,SAAG;EACD,YAAIL,OAAO,CAACqB,IAAR,CAAahB,OAAb,EAAsBC,QAAtB,CAAJ,EAAqC;EACnC,iBAAOD,OAAP;EACD;;EAEDA,QAAAA,OAAO,GAAGA,OAAO,CAACiB,aAAR,IAAyBjB,OAAO,CAACkB,UAA3C;EACD,OAND,QAMSlB,OAAO,KAAK,IAAZ,IAAoBA,OAAO,CAACmB,QAAR,KAAqB,CANlD;;EAQA,aAAO,IAAP;EACD,KAdD;EAeD;;EAED,MAAI,CAACvB,MAAM,CAACS,KAAR,IAAiB,OAAOT,MAAM,CAACS,KAAd,KAAwB,UAA7C,EAAyD;EACvDH,IAAAA,QAAQ,GAAG,kBAACC,MAAD,EAASC,MAAT,EAAoB;EAC7BA,MAAAA,MAAM,GAAGA,MAAM,IAAI,EAAnB;EACA,UAAMgB,CAAC,GAAGP,QAAQ,CAACQ,WAAT,CAAqB,OAArB,CAAV;EACAD,MAAAA,CAAC,CAACE,SAAF,CAAYnB,MAAZ,EAAoBoB,OAAO,CAACnB,MAAM,CAACoB,OAAR,CAA3B,EAA6CD,OAAO,CAACnB,MAAM,CAACqB,UAAR,CAApD;EACA,aAAOL,CAAP;EACD,KALD;EAMD;;EAED,MAAI,OAAOxB,MAAM,CAACa,WAAd,KAA8B,UAAlC,EAA8C;EAC5C,QAAMiB,oBAAoB,GAAG9B,MAAM,CAACS,KAAP,CAAaP,SAAb,CAAuB6B,cAApD;;EAEArB,IAAAA,iBAAiB,GAAG,2BAACC,SAAD,EAAYH,MAAZ,EAAuB;EACzC,UAAMwB,GAAG,GAAGf,QAAQ,CAACQ,WAAT,CAAqB,aAArB,CAAZ;EAEAjB,MAAAA,MAAM,GAAGA,MAAM,IAAI;EAAEoB,QAAAA,OAAO,EAAE,KAAX;EAAkBC,QAAAA,UAAU,EAAE,KAA9B;EAAqCI,QAAAA,MAAM,EAAE;EAA7C,OAAnB;EACAD,MAAAA,GAAG,CAACE,eAAJ,CAAoBvB,SAApB,EAA+BH,MAAM,CAACoB,OAAtC,EAA+CpB,MAAM,CAACqB,UAAtD,EAAkErB,MAAM,CAACyB,MAAzE;;EACAD,MAAAA,GAAG,CAACD,cAAJ,GAAqB,YAAY;EAC/B,YAAI,CAAC,KAAKF,UAAV,EAAsB;EACpB;EACD;;EAEDC,QAAAA,oBAAoB,CAACV,IAArB,CAA0B,IAA1B;EACAe,QAAAA,MAAM,CAACC,cAAP,CAAsB,IAAtB,EAA4B,kBAA5B,EAAgD;EAC9CC,UAAAA,GAAG,EAAE,eAAY;EAAE,mBAAO,IAAP;EAAa;EADc,SAAhD;EAGD,OATD;;EAWA,aAAOL,GAAP;EACD,KAjBD;EAkBD;EACF;;EAEDlB,QAAQ;;ECjER,IAAMwB,uBAAuB,GAAG,IAAhC;EAEA,IAAMC,SAAS,GAAG;EAChBC,EAAAA,MAAM,EAAE,QADQ;EAEhBC,EAAAA,MAAM,EAAE,QAFQ;EAGhBC,EAAAA,KAAK,EAAE,gBAHS;EAIhBC,EAAAA,IAAI,EAAE,eAJU;EAKhBC,EAAAA,IAAI,EAAE,MALU;EAMhBC,EAAAA,QAAQ,EAAE;EANM,CAAlB;EASA,IAAMC,kBAAkB,GAAG,eAA3B;EACA,IAAMC,cAAc,GAAG,WAAvB;;EAEA,IAAMC,IAAI,GAAG,SAAPA,IAAO,CAACC,WAAD,EAAcC,SAAd,EAAyBC,OAAzB,EAAkCC,IAAlC,EAA2C;EACtD,MAAMC,OAAO,GAAGJ,WAAW,CAACF,cAAD,CAA3B;;EAEA,MAAIM,OAAO,CAACC,MAAR,CAAeJ,SAAf,EAA0BK,SAA1B,CAAoCpC,QAApC,CAA6CoB,SAAS,CAACC,MAAvD,KAAkEa,OAAO,CAACG,cAAR,CAAuBN,SAAvB,EAAkCK,SAAlC,CAA4CpC,QAA5C,CAAqDoB,SAAS,CAACC,MAA/D,CAAtE,EAA8I;EAC5I;EACD;;EAED,MAAMiB,SAAS,GAAG/C,iBAAiB,CAAC,iBAAD,EAAoB;EACrDmB,IAAAA,UAAU,EAAE,IADyC;EAErDI,IAAAA,MAAM,EAAE;EACNyB,MAAAA,IAAI,EAAEL,OAAO,CAACM,aADR;EAENC,MAAAA,EAAE,EAAEV,SAFE;EAGNA,MAAAA,SAAS,EAATA;EAHM;EAF6C,GAApB,CAAnC;EAQAD,EAAAA,WAAW,CAACY,aAAZ,CAA0BJ,SAA1B;;EAEA,MAAMK,UAAU,GAAGT,OAAO,CAACC,MAAR,CAAeS,MAAf,CAAsB,UAAAC,IAAI;EAAA,WAAIA,IAAI,CAACT,SAAL,CAAepC,QAAf,CAAwBoB,SAAS,CAACC,MAAlC,CAAJ;EAAA,GAA1B,CAAnB;;EACA,MAAMyB,aAAa,GAAGZ,OAAO,CAACG,cAAR,CAAuBO,MAAvB,CAA8B,UAAAG,OAAO;EAAA,WAAIA,OAAO,CAACX,SAAR,CAAkBpC,QAAlB,CAA2BoB,SAAS,CAACC,MAArC,CAAJ;EAAA,GAArC,CAAtB;;EAEA,MAAIiB,SAAS,CAACU,gBAAd,EAAgC;EAC9B;EACD;;EAED,MAAIL,UAAU,CAACM,MAAf,EAAuB;EACrBN,IAAAA,UAAU,CAAC,CAAD,CAAV,CAAcP,SAAd,CAAwBc,MAAxB,CAA+B9B,SAAS,CAACC,MAAzC;EACD;;EACD,MAAIyB,aAAa,CAACG,MAAlB,EAA0B;EACxBH,IAAAA,aAAa,CAAC,CAAD,CAAb,CAAiBV,SAAjB,CAA2Bc,MAA3B,CAAkC9B,SAAS,CAACC,MAA5C;;EAEA,QAAI,CAACS,WAAW,CAACM,SAAZ,CAAsBpC,QAAtB,CAA+BoB,SAAS,CAACM,QAAzC,CAAD,IAAuD,CAACQ,OAAO,CAACF,OAAR,CAAgBmB,SAA5E,EAAuF;EACrFL,MAAAA,aAAa,CAAC,CAAD,CAAb,CAAiBV,SAAjB,CAA2Bc,MAA3B,CAAkC9B,SAAS,CAACG,KAA5C;EACD;EACF;;EAED6B,EAAAA,QAAQ,CAACtB,WAAD,EAAcI,OAAO,CAACC,MAAR,CAAeJ,SAAf,CAAd,EAAyCG,OAAO,CAACC,MAAjD,EAAyDH,OAAzD,CAAR;EACAqB,EAAAA,WAAW,CAACvB,WAAD,EAAcI,OAAO,CAACG,cAAR,CAAuBN,SAAvB,CAAd,EAAiDG,OAAO,CAACG,cAAzD,EAAyES,aAAzE,EAAwFb,IAAxF,CAAX;EACD,CArCD;;EAuCA,IAAMmB,QAAQ,GAAG,SAAXA,QAAW,CAACtB,WAAD,EAAce,IAAd,EAAoBS,QAApB,EAA8BtB,OAA9B,EAA0C;EACzDsB,EAAAA,QAAQ,CAACC,OAAT,CAAiB,UAAAV,IAAI,EAAI;EACvB,QAAMW,OAAO,GAAGX,IAAI,CAACY,aAAL,CAAmBzB,OAAO,CAAC0B,SAAR,CAAkBF,OAArC,CAAhB;EAEAA,IAAAA,OAAO,CAACG,YAAR,CAAqB,eAArB,EAAsC,OAAtC,EAHuB;;EAKvB,QAAI7B,WAAW,CAACM,SAAZ,CAAsBpC,QAAtB,CAA+BoB,SAAS,CAACE,MAAzC,CAAJ,EAAsD;EACpDkC,MAAAA,OAAO,CAACG,YAAR,CAAqB,UAArB,EAAiC,UAAjC;EACD;EACF,GARD;EAUAd,EAAAA,IAAI,CAACT,SAAL,CAAewB,GAAf,CAAmBxC,SAAS,CAACC,MAA7B;EACA,MAAMwC,cAAc,GAAGhB,IAAI,CAACY,aAAL,CAAmBzB,OAAO,CAAC0B,SAAR,CAAkBF,OAArC,CAAvB;EAEAK,EAAAA,cAAc,CAACF,YAAf,CAA4B,eAA5B,EAA6C,MAA7C,EAdyD;;EAgBzD,MAAI7B,WAAW,CAACM,SAAZ,CAAsBpC,QAAtB,CAA+BoB,SAAS,CAACE,MAAzC,CAAJ,EAAsD;EACpDuC,IAAAA,cAAc,CAACC,eAAf,CAA+B,UAA/B;EACD;EACF,CAnBD;;EAqBA,IAAMT,WAAW,GAAG,SAAdA,WAAc,CAACvB,WAAD,EAAciB,OAAd,EAAuBgB,WAAvB,EAAoCjB,aAApC,EAAmDb,IAAnD,EAA4D;EAC9E,MAAMC,OAAO,GAAGJ,WAAW,CAACF,cAAD,CAA3B;EACA,MAAMoC,OAAO,GAAGD,WAAW,CAACE,OAAZ,CAAoBlB,OAApB,CAAhB;EACA,MAAMmB,UAAU,GAAG3E,iBAAiB,CAAC,kBAAD,EAAqB;EACvDmB,IAAAA,UAAU,EAAE,IAD2C;EAEvDI,IAAAA,MAAM,EAAE;EACNyB,MAAAA,IAAI,EAAEL,OAAO,CAACM,aADR;EAENC,MAAAA,EAAE,EAAEuB,OAFE;EAGNjC,MAAAA,SAAS,EAAEiC;EAHL;EAF+C,GAArB,CAApC;;EASA,WAASG,QAAT,GAAqB;EACnBpB,IAAAA,OAAO,CAACX,SAAR,CAAkBwB,GAAlB,CAAsBxC,SAAS,CAACG,KAAhC;EACAwB,IAAAA,OAAO,CAACqB,mBAAR,CAA4BzC,kBAA5B,EAAgDwC,QAAhD;EACArC,IAAAA,WAAW,CAACY,aAAZ,CAA0BwB,UAA1B;EACAjC,IAAAA,IAAI;EACL;;EAED,MAAIc,OAAO,CAACX,SAAR,CAAkBpC,QAAlB,CAA2BoB,SAAS,CAACK,IAArC,CAAJ,EAAgD;EAC9CsB,IAAAA,OAAO,CAACX,SAAR,CAAkBc,MAAlB,CAAyB9B,SAAS,CAACI,IAAnC;EACA,QAAM6C,QAAQ,GAAGC,gCAAgC,CAACvB,OAAD,CAAjD;EAEAA,IAAAA,OAAO,CAACwB,gBAAR,CAAyB5C,kBAAzB,EAA6CwC,QAA7C;;EACA,QAAIrB,aAAa,CAACG,MAAlB,EAA0B;EACxBH,MAAAA,aAAa,CAAC,CAAD,CAAb,CAAiBV,SAAjB,CAA2BwB,GAA3B,CAA+BxC,SAAS,CAACI,IAAzC;EACD;;EAEDuB,IAAAA,OAAO,CAACX,SAAR,CAAkBwB,GAAlB,CAAsBxC,SAAS,CAACC,MAAhC;EACAmD,IAAAA,oBAAoB,CAACzB,OAAD,EAAUsB,QAAV,CAApB;EACD,GAXD,MAWO;EACLtB,IAAAA,OAAO,CAACX,SAAR,CAAkBwB,GAAlB,CAAsBxC,SAAS,CAACC,MAAhC;EACA0B,IAAAA,OAAO,CAACX,SAAR,CAAkBwB,GAAlB,CAAsBxC,SAAS,CAACG,KAAhC;EACAO,IAAAA,WAAW,CAACY,aAAZ,CAA0BwB,UAA1B;EACAjC,IAAAA,IAAI;EACL;EACF,CApCD;;EAsCA,IAAMqC,gCAAgC,GAAG,SAAnCA,gCAAmC,CAAArF,OAAO,EAAI;EAClD,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,CAAP;EACD,GAHiD;;;EAMlD,MAAIwF,kBAAkB,GAAG5F,MAAM,CAAC6F,gBAAP,CAAwBzF,OAAxB,EAAiCwF,kBAA1D;EACA,MAAME,uBAAuB,GAAGC,UAAU,CAACH,kBAAD,CAA1C,CAPkD;;EAUlD,MAAI,CAACE,uBAAL,EAA8B;EAC5B,WAAO,CAAP;EACD,GAZiD;;;EAelDF,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACI,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;EAEA,SAAOD,UAAU,CAACH,kBAAD,CAAV,GAAiCtD,uBAAxC;EACD,CAlBD;;EAoBA,IAAMqD,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACvF,OAAD,EAAUoF,QAAV,EAAuB;EAClD,MAAIS,MAAM,GAAG,KAAb;EACA,MAAMC,eAAe,GAAG,CAAxB;EACA,MAAMC,gBAAgB,GAAGX,QAAQ,GAAGU,eAApC;;EACA,WAASE,QAAT,GAAqB;EACnBH,IAAAA,MAAM,GAAG,IAAT;EACA7F,IAAAA,OAAO,CAACmF,mBAAR,CAA4BzC,kBAA5B,EAAgDsD,QAAhD;EACD;;EAEDhG,EAAAA,OAAO,CAACsF,gBAAR,CAAyB5C,kBAAzB,EAA6CsD,QAA7C;EACApG,EAAAA,MAAM,CAACqG,UAAP,CAAkB,YAAM;EACtB,QAAI,CAACJ,MAAL,EAAa;EACX7F,MAAAA,OAAO,CAACyD,aAAR,CAAsBvD,QAAQ,CAACwC,kBAAD,CAA9B;EACD;;EAED1C,IAAAA,OAAO,CAACmF,mBAAR,CAA4BzC,kBAA5B,EAAgDsD,QAAhD;EACD,GAND,EAMGD,gBANH;EAOD,CAjBD;;EAmBA,IAAMG,eAAe,GAAG,SAAlBA,eAAkB,CAACpB,WAAD,EAAc/B,OAAd,EAA0B;EAChD,MAAIA,OAAO,CAACmB,SAAZ,EAAuB;EACrBY,IAAAA,WAAW,CAACR,OAAZ,CAAoB,UAAAR,OAAO,EAAI;EAC7BA,MAAAA,OAAO,CAACX,SAAR,CAAkBwB,GAAlB,CAAsBxC,SAAS,CAACK,IAAhC;EACAsB,MAAAA,OAAO,CAACX,SAAR,CAAkBwB,GAAlB,CAAsBxC,SAAS,CAACI,IAAhC;EACD,KAHD;EAID;EACF,CAPD;;ECtJA,IAAM4D,4BAA4B,GAAG,SAA/BA,4BAA+B;EAAA,SAAM,SAASC,uBAAT,CAAkCC,KAAlC,EAAyC;EAClFA,IAAAA,KAAK,CAAC1E,cAAN;EACD,GAFoC;EAAA,CAArC;;EAIA,IAAM2E,+BAA+B,GAAG,SAAlCA,+BAAkC,CAAAvD,OAAO;EAAA,SAAI,SAASwD,0BAAT,CAAqCF,KAArC,EAA4C;EAC7FA,IAAAA,KAAK,CAAC1E,cAAN;EAEA,QAAMiC,IAAI,GAAG7D,OAAO,CAACsG,KAAK,CAACG,MAAP,EAAezD,OAAO,CAAC0B,SAAR,CAAkBgC,KAAjC,CAApB;EACA,QAAM5D,WAAW,GAAG9C,OAAO,CAAC6D,IAAD,EAAOb,OAAO,CAAC0B,SAAR,CAAkBxB,OAAzB,CAA3B;EACA,QAAMA,OAAO,GAAGJ,WAAW,CAACF,cAAD,CAA3B;;EACA,QAAM+D,SAAS,GAAGzD,OAAO,CAACC,MAAR,CAAe8B,OAAf,CAAuBpB,IAAvB,CAAlB;;EAEAhB,IAAAA,IAAI,CAACC,WAAD,EAAc6D,SAAd,EAAyB3D,OAAzB,EAAkC,YAAM;EAC1CE,MAAAA,OAAO,CAACM,aAAR,GAAwBmD,SAAxB;EACD,KAFG,CAAJ;EAGD,GAX8C;EAAA,CAA/C;;ECJA,IAAMC,eAAe,GAAG;EACtBC,EAAAA,MAAM,EAAE,IADc;EAEtB1C,EAAAA,SAAS,EAAE,KAFW;EAGtBO,EAAAA,SAAS,EAAE;EACTgC,IAAAA,KAAK,EAAE,OADE;EAETlC,IAAAA,OAAO,EAAE,eAFA;EAGTtB,IAAAA,OAAO,EAAE;EAHA;EAHW,CAAxB;;MAUM4D;;;EACJ,mBAAa7G,OAAb,EAAsB8G,QAAtB,EAAqC;EAAA;;EAAA,QAAfA,QAAe;EAAfA,MAAAA,QAAe,GAAJ,EAAI;EAAA;;EACnC,SAAKC,QAAL,GAAgB/G,OAAhB;EACA,SAAKuD,aAAL,GAAqB,CAArB;EACA,SAAKH,cAAL,GAAsB,EAAtB;EAEA,SAAKL,OAAL,gBACK4D,eADL,MAEKG,QAFL;EAKA,SAAK/D,OAAL,CAAa0B,SAAb,gBACKkC,eAAe,CAAClC,SADrB,MAEK,KAAK1B,OAAL,CAAa0B,SAFlB;;EAKA,QAAI,KAAK1B,OAAL,CAAa6D,MAAjB,EAAyB;EACvB,WAAKG,QAAL,CAAc5D,SAAd,CAAwBwB,GAAxB,CAA4BxC,SAAS,CAACE,MAAtC;EACD;;EAED,SAAKa,MAAL,GAAc,GAAG8D,KAAH,CAAShG,IAAT,CAAc,KAAK+F,QAAL,CAAcE,gBAAd,CAA+B,KAAKlE,OAAL,CAAa0B,SAAb,CAAuBgC,KAAtD,CAAd,CAAd;;EAEA,SAAKvD,MAAL,CAAYS,MAAZ,CAAmB,UAAAC,IAAI;EAAA,aAAIA,IAAI,CAACsD,YAAL,CAAkB,aAAlB,CAAJ;EAAA,KAAvB,EACG5C,OADH,CACW,UAAAV,IAAI,EAAI;EACf,MAAA,KAAI,CAACR,cAAL,CAAoB+D,IAApB,CACE,KAAI,CAACJ,QAAL,CAAcvC,aAAd,CAA4BZ,IAAI,CAACwD,YAAL,CAAkB,aAAlB,CAA5B,CADF;EAGD,KALH;;EAOAlB,IAAAA,eAAe,CAAC,KAAK9C,cAAN,EAAsB,KAAKL,OAA3B,CAAf;;EACA,SAAKsE,iBAAL;;EACAtF,IAAAA,MAAM,CAACC,cAAP,CAAsB,KAAK+E,QAA3B,EAAqCpE,cAArC,EAAqD;EACnD2E,MAAAA,KAAK,EAAE,IAD4C;EAEnDC,MAAAA,QAAQ,EAAE;EAFyC,KAArD;;EAKA,QAAI,KAAKrE,MAAL,CAAYc,MAAhB,EAAwB;EACtBpB,MAAAA,IAAI,CAAC,KAAKmE,QAAN,EAAgB,KAAKxD,aAArB,EAAoC,KAAKR,OAAzC,EAAkD,YAAM,EAAxD,CAAJ;EACD;EACF;;;;;WAIDsE,oBAAA,6BAAqB;EAAA;;EACnB,SAAKnE,MAAL,CAAYoB,OAAZ,CAAoB,UAAAV,IAAI,EAAI;EAC1B,UAAMW,OAAO,GAAGX,IAAI,CAACY,aAAL,CAAmB,MAAI,CAACzB,OAAL,CAAa0B,SAAb,CAAuBF,OAA1C,CAAhB;;EAEA,UAAI,MAAI,CAACxB,OAAL,CAAa6D,MAAjB,EAAyB;EACvB,QAAA,MAAI,CAACY,wBAAL,GAAgCrB,4BAA4B,CAAC,MAAI,CAACpD,OAAN,CAA5D;EACAwB,QAAAA,OAAO,CAACe,gBAAR,CAAyB,OAAzB,EAAkC,MAAI,CAACkC,wBAAvC;EACD,OAHD,MAGO;EACL,QAAA,MAAI,CAACC,2BAAL,GAAmCnB,+BAA+B,CAAC,MAAI,CAACvD,OAAN,CAAlE;EACAwB,QAAAA,OAAO,CAACe,gBAAR,CAAyB,OAAzB,EAAkC,MAAI,CAACmC,2BAAvC;EACD;EACF,KAVD;EAWD;;;WAIDC,OAAA,gBAAQ;EAAA;;EACN,QAAMC,QAAQ,GAAI,KAAKpE,aAAL,GAAqB,CAAtB,IAA4B,KAAKL,MAAL,CAAYc,MAAZ,GAAqB,CAAjD,GAAqD,KAAKT,aAAL,GAAqB,CAA1E,GAA+E,KAAKL,MAAL,CAAYc,MAAZ,GAAqB,CAArH;EAEApB,IAAAA,IAAI,CAAC,KAAKmE,QAAN,EAAgBY,QAAhB,EAA0B,KAAK5E,OAA/B,EAAwC,YAAM;EAChD,MAAA,MAAI,CAACQ,aAAL,GAAqBoE,QAArB;EACD,KAFG,CAAJ;EAGD;;WAEDC,WAAA,oBAAY;EAAA;;EACV,QAAMC,YAAY,GAAI,KAAKtE,aAAL,GAAqB,CAAtB,IAA4B,CAA5B,GAAgC,KAAKA,aAAL,GAAqB,CAArD,GAAyD,CAA9E;EAEAX,IAAAA,IAAI,CAAC,KAAKmE,QAAN,EAAgBc,YAAhB,EAA8B,KAAK9E,OAAnC,EAA4C,YAAM;EACpD,MAAA,MAAI,CAACQ,aAAL,GAAqBsE,YAArB;EACD,KAFG,CAAJ;EAGD;;WAEDrE,KAAA,YAAIsE,UAAJ,EAAgB;EAAA;;EACd,QAAMC,SAAS,GAAGD,UAAU,GAAG,CAA/B;EACA,QAAMH,QAAQ,GAAGI,SAAS,IAAI,CAAb,IAAkBA,SAAS,GAAG,KAAK7E,MAAL,CAAYc,MAA1C,GACb+D,SADa,GAEb,CAFJ;EAIAnF,IAAAA,IAAI,CAAC,KAAKmE,QAAN,EAAgBY,QAAhB,EAA0B,KAAK5E,OAA/B,EAAwC,YAAM;EAChD,MAAA,MAAI,CAACQ,aAAL,GAAqBoE,QAArB;EACD,KAFG,CAAJ;EAGD;;WAEDK,QAAA,iBAAS;EAAA;;EACPpF,IAAAA,IAAI,CAAC,KAAKmE,QAAN,EAAgB,CAAhB,EAAmB,KAAKhE,OAAxB,EAAiC,YAAM;EACzC,MAAA,MAAI,CAACQ,aAAL,GAAqB,CAArB;EACD,KAFG,CAAJ;EAGD;;WAED0E,UAAA,mBAAW;EAAA;;EACT,SAAK/E,MAAL,CAAYoB,OAAZ,CAAoB,UAAAV,IAAI,EAAI;EAC1B,UAAMW,OAAO,GAAGX,IAAI,CAACY,aAAL,CAAmB,MAAI,CAACzB,OAAL,CAAa0B,SAAb,CAAuBF,OAA1C,CAAhB;;EAEA,UAAI,MAAI,CAACxB,OAAL,CAAa6D,MAAjB,EAAyB;EACvBrC,QAAAA,OAAO,CAACY,mBAAR,CAA4B,OAA5B,EAAqC,MAAI,CAACqC,wBAA1C;EACD,OAFD,MAEO;EACLjD,QAAAA,OAAO,CAACY,mBAAR,CAA4B,OAA5B,EAAqC,MAAI,CAACsC,2BAA1C;EACD;EACF,KARD;;EAUA,SAAKV,QAAL,CAAcpE,cAAd,IAAgCuF,SAAhC;EACA,SAAKnB,QAAL,GAAgBmB,SAAhB;EACA,SAAK3E,aAAL,GAAqB2E,SAArB;EACA,SAAKhF,MAAL,GAAcgF,SAAd;EACA,SAAK9E,cAAL,GAAsB8E,SAAtB;EACA,SAAKV,wBAAL,GAAgCU,SAAhC;EACA,SAAKT,2BAAL,GAAmCS,SAAnC;EACD;;;;;;;;;;;"}