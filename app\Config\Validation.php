<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;
use CodeIgniter\Validation\StrictRules\CreditCardRules;
use CodeIgniter\Validation\StrictRules\FileRules;
use CodeIgniter\Validation\StrictRules\FormatRules;
use CodeIgniter\Validation\StrictRules\Rules;

class Validation extends BaseConfig
{
    // --------------------------------------------------------------------
    // Setup
    // --------------------------------------------------------------------

    /**
     * Stores the classes that contain the
     * rules that are available.
     *
     * @var list<string>
     */
    public array $ruleSets = [
        Rules::class,
        FormatRules::class,
        FileRules::class,
        CreditCardRules::class,
    ];

    /**
     * Specifies the views that are used to display the
     * errors.
     *
     * @var array<string, string>
     */
    public array $templates = [
        'list'   => 'CodeIgniter\Validation\Views\list',
        'single' => 'CodeIgniter\Validation\Views\single',
    ];

    // --------------------------------------------------------------------
    // Rules
    // --------------------------------------------------------------------

    public $login = [
        // "email" => ["label"=>"Email","rules"=>"required"],
        "username" => ["label"=>"Username","rules"=>"required"],
        "password" => ["label"=>"Password","rules"=>"required"],
    ];

    public $adminLogin = [
        "username" => ["label"=>"Username","rules"=>"required"],
        "password" => ["label"=>"Password","rules"=>"required"],
    ];

    public $createReceivable = [
        "proj_description" => ["label"=>"Project","rules"=>"required"], //project id from busmatch
        // "recno" => ["label"=>"Record Number","rules"=>"required"],
        // "fair_code" => ["label"=>"Fair code","rules"=>"required"],
        // "sector" => ["label"=>"Sector","rules"=>"required"],
        // "fair_description" => ["label"=>"Fair Description","rules"=>"required"],
        // "event" => ["label"=>"Event","rules"=>"required"],
        // "start_date" => ["label"=>"Start Date","rules"=>"required"],
        // "end_date" => ["label"=>"End Date","rules"=>"required"],
        "company_id" => ["label"=>"Company","rules"=>"required"], 

        "address" => ["label"=>"Address","rules"=>"required"],
        "tin_no" => ["label"=>"TIN Number","rules"=>"required"],
        "contact_person" => ["label"=>"Contact Person","rules"=>"required"],
        "co_email" => ["label"=>"Company Email","rules"=>"required"],
        // "tel_off" => ["label"=>"Office Telephone","rules"=>"required"],
        // "co_mobile" => ["label"=>"Company Mobile","rules"=>"required"],

        "invoice_no" => ["label"=>"Invoice number","rules"=>"required"],
        "invoice_date" => ["label"=>"Invoice Date","rules"=>"required"],
        "participation_fee" => ["label"=>"Participation Fee","rules"=>"required"],
        "participation_type" => ["label"=>"Participation Type","rules"=>"required"],
        // "subsidy_amount" => ["label"=>"Subsidy Cost","rules"=>"required"],
        // "net_receivable_amount" => ["label"=>"Net Receivable Amount","rules"=>"required"],
        // "description" => ["label"=>"Receivable Description","rules"=>"required"],

    ];

    public $createModalPayment = [
        "amount_paid" => ["label"=>"Payment Amount","rules"=>"required"],
        "payment_date" => ["label"=>"Payment Date","rules"=>"required"],
        // "reference_no" => ["label"=>"Reference Number","rules"=>"required"],
        // "remarks" => ["label"=>"Note","rules"=>"required"],
    ];
    public $retailSalePost = [
        "prod_code" => ["label"=>"Product","rules"=>"required"],
        "buyer_name" => ["label"=>"Buyer name","rules"=>"required"],
        "sale_date" => ["label"=>"Date of Sale","rules"=>"required"],
        // "type" => ["label"=>"Sales type","rules"=>"required"],
        "cost" => ["label"=>"Cost","rules"=>"required"],
    ];
    public $inquiryPost = [
        "date" => ["label"=>"Date","rules"=>"required"],
        "buyers_met_no" => ["label"=>"Number of Buyers Met","rules"=>"required"],
        "inquiry_no" => ["label"=>"Number of Inquiries","rules"=>"required"],
    ];
    public $updatedSalePost = [
        "updated_status" => ["label"=>"Updated Status","rules"=>"required"],
        "updated_cost" => ["label"=>"Updated Cost","rules"=>"required"],
    ];

    
}
