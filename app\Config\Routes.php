<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

$routes->get('/fill-form', 'FormFillerController::fillform');

$routes->get('/testform', 'FormFillerController::testform');

$routes->match(['get', 'post'], 'pdf/generate', 'FormFillerController::generate');

$routes->get('/testform2', 'FormFillerController::testtextarea');

$routes->match(['get', 'post'], 'pdf/overlay-textarea', 'FormFillerController::overlayTextarea');










$routes->group("dashboard",["namespace"=>"App\Controllers",'filter'=>'login'],function($routes){
    $routes->get('/', 'DashboardController::index');
    $routes->get('aging-data/', 'DashboardController::ajxAgingData');
    $routes->get('aging-summary-data/', 'DashboardController::ajxAgingSummary');
    $routes->get('outstanding-receivable-data/', 'DashboardController::ajxOutstandingReceivable');
});


$routes->group('reports', function ($routes) {
    // General Ledger
    $routes->get('ledger', 'ReceivablesController::generalLedger');
    $routes->post('ledger/data', 'ReceivablesController::getGeneralLedgerData');
    // Summary Report
    $routes->get('summary', 'ReceivablesController::summaryReport');
    $routes->post('summary/data', 'ReceivablesController::getSummaryData');
    // Aging Report
    $routes->get('aging', 'ReceivablesController::agingReport');
    $routes->post('aging/data', 'ReceivablesController::getAgingData'); 
    // Detailed Receivables Report
    $routes->get('detailed', 'ReceivablesController::detailedReport'); 
    $routes->post('detailed/data', 'ReceivablesController::getDetailedData');
    // Receivables by Exhibitor
    $routes->get('by-exhibitor', 'ReceivablesController::receivableByExhibitor'); 
    $routes->post('by-exhibitor/data', 'ReceivablesController::getReceivableByExhibitorData');
    // Receivables by Project
    $routes->get('by-project', 'ReceivablesController::receivableByProject');
    $routes->post('by-project/data', 'ReceivablesController::getReceivableByProjectData'); 
    // Receivables by TIN
    $routes->get('by-tin', 'ReceivablesController::receivableByTIN');
    $routes->post('by-tin/data', 'ReceivablesController::getReceivableByTINData');

    $routes->get('test', 'ReportsController::test');
});

$routes->group("exhibitors",["namespace"=>"App\Controllers"],function($routes){
    $routes->get('/', 'ExhibitorController::index',['filter'=>'login']);
    $routes->get('getAll/','ExhibitorController::ajxGetAllExhibitors',['filter'=>'login']);
    $routes->get('profile/(:num)','ExhibitorController::show/$1',['filter'=>'login']);
    $routes->get('(:num)/receivables-history','ExhibitorController::ajxReceivablesHistory/$1',['filter'=>'login']);
    $routes->get('(:num)/payments-history','ExhibitorController::ajxPaymentsHistory/$1',['filter'=>'login']);

});


$routes->group("receivables",["namespace"=>"App\Controllers"],function($routes){
    $routes->get('/', 'ReceivablesController::index',['filter'=>'login']);
    $routes->get('new/', 'ReceivablesController::new',['filter'=>'login']);
    $routes->post('/','ReceivablesController::create',['filter'=>'login']);
    $routes->get('(:num)/edit','ReceivablesController::edit/$1',['filter'=>'login']);
    $routes->post('(:num)/update/','ReceivablesController::update/$1',['filter'=>'login']);
    $routes->delete('(:num)/delete','ReceivablesController::delete/$1',['filter'=>'login']);
    $routes->get('busmatchdetails/(:num)','ReceivablesController::ajxExhByBusMatchDetails/$1',['filter'=>'login']);
    $routes->get('getexhdetails/(:num)','ReceivablesController::ajxGetExhDetails/$1',['filter'=>'login']);
    $routes->get('loadExhibitorList/(:num)','ReceivablesController::ajxLoadExhibitorList/$1',['filter'=>'login']);

    $routes->get('getAll/','ReceivablesController::ajxGetAllReceivables',['filter'=>'login']);
    $routes->post('cancel','ReceivablesController::ajxCancelReceivables',['filter'=>'login']);
    
    $routes->get('exhibitor/(:num)', 'ReceivablesController::ajaxGetByExhibitor/$1',['filter'=>'login']);
    $routes->get('exhibitor2/(:num)', 'ReceivablesController::ajaxGetByExhibitor2/$1',['filter'=>'login']);
    $routes->post('payment/', 'ReceivablesController::applyPayment',['filter'=>'login']);
    
});


$routes->group("payments",["namespace"=>"App\Controllers"],function($routes){
    $routes->get('pick-exhibitor', 'PaymentController::pickExhibitor');
    //$routes->post('/', 'PaymentController::store'); // Record a new payment in the db
    $routes->get('exhibitor/(:num)', 'PaymentController::getByExhibitor/$1'); // Fetch payments for an exhibitor
    $routes->get('receivable/(:num)', 'PaymentController::getByReceivable/$1'); // Fetch payments for a receivable
    $routes->get('multi/', 'PaymentController::multiPayment');
    $routes->post('process/', 'PaymentController::store');
    
});


// $routes->group("auth",["namespace"=>"App\Controllers\Auth"],function($routes){
//     $routes->get('/', 'LoginController::login');
//     // $routes->post("register","SignupController::register");
//     $routes->post("verify_login","LoginController::verifyLogin");
//     $routes->get("profile","AuthController::profile");
//     $routes->get("logout","LoginController::logout");
// });


$routes->group("admin",["namespace"=>"App\Controllers\Admin"],function($routes){
    $routes->get('/', 'DashboardController::index');
    $routes->post('verify_login/', 'AuthController::authenticate');
    $routes->get('logout/', 'AuthController::logout',['filter'=>'login']);
    // $routes->get('auth', 'AuthController::index');
    $routes->get('dashboard', 'DashboardController::index',['filter'=>'login']);
});



// Menu Management Module Routes
if (file_exists(ROOTPATH . 'Modules/MenuManagement/Config/Routes.php')) {
    require ROOTPATH . 'Modules/MenuManagement/Config/Routes.php';
}