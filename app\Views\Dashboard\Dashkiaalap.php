<?= $this->extend('layouts/dashboard'); ?>

<?= $this->section('title'); ?>Sales Monitoring<?= $this->endSection(); ?>

<?= $this->section('header'); ?>

<?= $this->endSection(); ?>

<?= $this->section('content'); ?>
<div class="container mt-4">
        <h3>My Events</h3>
        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Event 1</h5>
                        <p class="card-text">Details about Event 1.</p>
                        <a href="/exhibitor/event/1" class="btn btn-primary">Manage Sales</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Event 2</h5>
                        <p class="card-text">Details about Event 2.</p>
                        <a href="/exhibitor/event/2" class="btn btn-primary">Manage Sales</a>
                    </div>
                </div>
            </div>
            <!-- More events as needed -->
        </div>
    </div>
<?= $this->endSection(); ?>

<?= $this->section("script") ?>
<!-- DataTables  & Plugins -->
<script src="https://cdn.datatables.net/2.1.3/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/responsive/3.0.2/js/dataTables.responsive.js"></script>
<script src="https://cdn.datatables.net/responsive/3.0.2/js/responsive.dataTables.js"></script>
<script src="<?= base_url("assets/")?>plugins/jquery-validation/jquery.validate.min.js"></script>
<script src="<?= base_url("assets/")?>plugins/inputmask/jquery.inputmask.js"></script>
<script src="<?= base_url("assets/")?>dist/js/sms.js"></script>

<script>

$(document).ready(function() {


});

</script>
<?= $this->endSection() ?>