<?php  

// if (!function_exists('load_rules')) {
//     function load_rules($group, array $rules = null) {
//         $validator = service('validation');
//         $existing_rules = $validator->getRuleGroup($group);
//         if (is_array($rules)) {
//             if($existing_rules){
//                 // Merge existing and provided rules (prioritize provided rules)
//                 $merged_rules = array_merge($rules, $existing_rules);
//             } else {
//                 $merged_rules = $rules;
//             }
//         } else {
//             $merged_rules = $existing_rules;
//         }
//         return $merged_rules ?: [];
//     }
// }

if (!function_exists('load_rules')) {
    function load_rules($group, array $rules = [], array $additional_rules = []) {
        $validator = service('validation');
        $existing_rules = $validator->getRuleGroup($group) ?? [];

        // Prioritize provided rules over existing rules
        $merged_rules = array_merge($existing_rules, $rules, $additional_rules);

        return $merged_rules;
    }
}

if(!function_exists('show_validation_error')){
    function show_validation_error($needle=null, $haystack=null){
    	if(is_array($haystack)){
    		return array_key_exists($needle, $haystack) ? $haystack[$needle] : false;
        }
      	return false;
    }
}


if(!function_exists('extractNoOfDays')){
    function extractNoOfDays($startDate, $noOfDays) {
        $eventDays = [];
        $date = date_create($startDate);
        for ($i = 0; $i < $noOfDays; $i++) {
            $eventDays[] = [
                'day' => 'Day '.$i + 1,
                'eventDate' => date_format($date, 'Y-m-d'),
            ];
            date_add($date, date_interval_create_from_date_string("1 day"));
        }
        return $eventDays;
    }
}
if(!function_exists('convert_to_mysql_date')){
    function convert_to_mysql_date($date_string) {
        $timestamp = strtotime($date_string);
        if ($timestamp === false) {
            return false; // Handle invalid date format
        }
        return date('Y-m-d', $timestamp);
    }
}


if(!function_exists('isInvalid')){
    function isInvalid($needle){
    	if(session()->has('errors')){
    		if(is_array(session("errors"))){
	    		return array_key_exists($needle, session("errors")) ? 'is-invalid' : false;
	        }
	        else{
	        	return 'valid';
	        }
    	}
    }
}


if(!function_exists('getActiveNav')){
    function getActiveNav($page){
        $currentPage = getCurrentPage();
        // Compare the current page with the expected page
        if ($currentPage == $page){
            return 'active'; // or any class name you prefer for highlighting
        }else{
            return '';
        }
    }
}



if(!function_exists('getCurrentPage')){
    function getCurrentPage(){
        $uri = service('uri');
        return $uri->getSegment(1) . '/' . $uri->getSegment(2);
    }
}

if(!function_exists('cleanAssocArr')){
    function cleanAssocArr($arr){
        $newArr = array();
        foreach($arr as $key=>$value){
            if($value!==''){
                $newArr[$key]=$value;
            }
        }
        return $newArr;
    }
}

if (!function_exists('renderSidebarMenu')) {
    function renderSidebarMenu($userId=null) {
        try {
            $menuService = new \Modules\MenuManagement\Services\MenuService();
            return $menuService->renderSidebarMenu($userId);
        } catch (Exception $e) {
            // Log error and return empty string
            log_message('error', 'renderSidebarMenu error: ' . $e->getMessage());
            return '';
        }
    }
}

if (!function_exists('getMenuBreadcrumb')) {
    function getMenuBreadcrumb($userId = null) {
        $menuService = new \Modules\MenuManagement\Services\MenuService();
        return $menuService->getBreadcrumb($userId);
    }
}

?>