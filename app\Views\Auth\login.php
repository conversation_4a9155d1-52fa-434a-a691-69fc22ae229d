<?= $this->extend('layouts/auth'); ?>

<?= $this->section('title'); ?>LOGIN | CARMS<?= $this->endSection(); ?>

<?= $this->section('content'); ?>


<div class="login-box">
  <!-- /.login-logo -->
  <div class="card card-outline ">
    <div class="card-header text-center">
      <a href="login" class="h3"><b><span style="font-size: 33px;color: #333;">Accounts Receivable Monitoring</span></b></a>
    </div>
    <div class="card-body">
      <!-- <p class="login-box-msg">Login</p> -->
      
      <?= form_open("auth/verify_login");?>

        <!-- display flash data message -->
        <?php if(session()->has('warning')): ?>
        <div class="alert alert-warning alert-dismissible"> 
                  <?php echo session()->get('warning'); ?>                  
                </div>
      <?php endif; ?>
        <?php
            if(session()->getFlashdata('success')) { ?>
                <div class="alert alert-success alert-dismissible"> 
                  test                   
                    <?php echo session()->getFlashdata('success') ?>
                </div>
            <?php } elseif(session()->getFlashdata('failed')) { ?>
                <div class="alert alert-danger alert-dismissible">
                    <?php echo session()->getFlashdata('failed') ?>
                </div>
        <?php } ?>

        <div class="input-group mb-3 has-validation">
          <input type="text" class="form-control <?php echo isInvalid('username') ?>" name="username" placeholder="Username" value="<?php echo old('username') ?>"/>          
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-envelope"></span>
            </div>
          </div>
              <div class="invalid-feedback">
                  <?php echo show_validation_error('username',session("errors"));?>
              </div>                                
        </div>
        <div class="input-group mb-3">
          <input id="password" type="password" class="form-control <?php echo isInvalid('password') ?>" name="password" placeholder="Password" value=""/>
          <div class="input-group-append">
            <div class="input-group-text">
              <span id="eye" class="fas fa-eye"></span>
            </div>
          </div>
                <div class="invalid-feedback">
                    <?php echo show_validation_error('password',session("errors"));?>
                </div>                                
        </div>

        <div class="row">

          <!-- /.col -->
          <div class="col-4">
            <button type="submit" name="submit" value="1" class="btn btn-primary btn-block">Login</button>
          </div>
          <!-- /.col -->
        </div>
      </form>


     <br>
      <p class="mb-1">
        <!-- <a href="<?//= site_url().'auth/forgot_password' ?>">I forgot my password</a> -->
      </p>
      <!-- 
      <p class="mb-0">
        <a href="register.html" class="text-center">Register a new membership</a>
      </p>
       -->

    </div>
    <!-- /.card-body -->
  </div>
  <!-- /.card -->
</div>
<!-- /.login-box -->
<?= $this->endSection(); ?>


<?= $this->section('script'); ?>


<script>
const passwordInput = document.querySelector("#password")
const eye = document.querySelector("#eye")
console.log(passwordInput);
console.log(eye);


eye.addEventListener("click", function(){
  this.classList.toggle("fa-eye-slash")
  const type = passwordInput.getAttribute("type") === "password" ? "text" : "password"
  passwordInput.setAttribute("type", type)
})

$(document).ready(function(){
  $('#eventSector').change(function(){ 
     if (document.getElementById('eventSector').value === "") {
      //alert(myVar1);
      $('#eventProj').prop('disabled', true);
     }
     else {
      $('#eventProj').prop('disabled', false);
     }
  });
});

</script>

<?= $this->endSection(); ?>