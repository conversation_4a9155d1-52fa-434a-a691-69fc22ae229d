<?php

namespace App\Controllers;

use App\Controllers\AdminController;
use CodeIgniter\HTTP\ResponseInterface;

class ApprovalHierarchyController extends AdminController
{
    protected $approvalHierarchyModel;

    public function __construct()
    {
        $this->approvalHierarchyModel = new ApprovalHierarchyModel();
    }

    public function index()
    {
        if (!hasPermission('manage_approval_hierarchy')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage approval hierarchy.');
        }

        $data['hierarchy'] = $this->approvalHierarchyModel->orderBy('level', 'asc')->findAll();
        $data['title'] = 'Manage Approval Hierarchy';
        $data['page_title'] = 'Approval Hierarchy';
        return view('admin/approval_hierarchy/index', $data);
    }

    public function create()
    {
        if (!hasPermission('manage_approval_hierarchy')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage approval hierarchy.');
        }

        helper(['form']);
        $data = [
            'title'      => 'Create Approval Level',
            'page_title' => 'Create Approval Level',
        ];

        if ($this->request->getMethod() == 'post') {
            if (! $this->validate($this->approvalHierarchyModel->getValidationRules())) {
                $data['validation'] = $this->validator;
            } else {
                $this->approvalHierarchyModel->save([
                    'name'        => $this->request->getVar('name'),
                    'description' => $this->request->getVar('description'),
                    'level'       => $this->request->getVar('level'),
                ]);
                return redirect()->to(base_url('admin/approval_hierarchy'))->with('success', 'Approval level created successfully.');
            }
        }
        return view('admin/approval_hierarchy/create', $data);
    }

    public function edit($id = null)
    {
        if (!hasPermission('manage_approval_hierarchy')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage approval hierarchy.');
        }

        helper(['form']);
        $level = $this->approvalHierarchyModel->find($id);

        if (empty($level)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Cannot find the approval level: ' . $id);
        }

        $data = [
            'level'      => $level,
            'title'      => 'Edit Approval Level',
            'page_title' => 'Edit Approval Level',
        ];

        if ($this->request->getMethod() == 'post') {
            if (! $this->validate($this->approvalHierarchyModel->getValidationRules())) {
                $data['validation'] = $this->validator;
            } else {
                $this->approvalHierarchyModel->update($id, [
                    'name'        => $this->request->getVar('name'),
                    'description' => $this->request->getVar('description'),
                    'level'       => $this->request->getVar('level'),
                ]);
                return redirect()->to(base_url('admin/approval_hierarchy'))->with('success', 'Approval level updated successfully.');
            }
        }
        return view('admin/approval_hierarchy/edit', $data);
    }

    public function delete($id = null)
    {
        if (!hasPermission('manage_approval_hierarchy')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage approval hierarchy.');
        }

        if ($this->approvalHierarchyModel->delete($id)) {
            return redirect()->to(base_url('admin/approval_hierarchy'))->with('success', 'Approval level deleted successfully.');
        } else {
            return redirect()->to(base_url('admin/approval_hierarchy'))->with('error', 'Could not delete approval level.');
        }
    }
}
