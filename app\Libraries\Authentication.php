<?php
namespace App\Libraries;

class Authentication{

	public function login($username,$password){
		$model = new \App\Models\UsersModel;

		// $user = $model->where('email',$email)->first();
		$user = $model->where('username',$username)->first();

		if($user===null){
			return false;
		}
		if(!password_verify($password,$user->password)){
			return false;
		}
		$session = session();
		$session->regenerate();
		$session->set('user',$user->id);
		return true;
	}

	public function adminLogin($username,$password){
		$model = new \App\Models\UserModel;

		$user = $model->where(['username'=>$username,'active'=>1])->first();

		if($user===null){
			return false;
		}
		if($password!=$user->password){
			return false;
		}
		$session = session();
		$session->regenerate();
		$admin = ($user->role=='admin') ? true : false;
		$session->set('user',$user->userid);
		$session->set('isAdmin',$admin);
		$session->set('lname',$user->lastname);
		$session->set('fname',$user->firstname);
		return true;
	}

	public function cinLogin($email,$cip,$fair_code){
		$model = new \App\Models\CipModel;
		$user = $model->where(['co_email'=>$email,'fair_code'=>$fair_code])->first();
		if($user===null){
			return false;
		}
		if($cip!=$user->cip){
			return false;
		}
		$session = session();
		$session->regenerate();
		$session->set('user',$user->ff_code);
		return true;
	}

	public function logout(){
		session()->destroy();
	}

	public function getCurrentUser(){
		if(!session()->has('user')){
			return null;
		}
	}

	public function isLoggedIn(){
		return session()->has('user');
	}





}