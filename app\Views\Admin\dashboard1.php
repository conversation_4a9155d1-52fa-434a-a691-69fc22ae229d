<?= $this->extend('layouts/dashboard'); ?>

<?= $this->section('title'); ?>Sales Monitoring<?= $this->endSection(); ?>

<?= $this->section('header'); ?>

<?= $this->endSection(); ?>

<?= $this->section('content'); ?>

  <!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <div class="container-fluid">
      <div class="row mb-2">
        <div class="col-sm-6">
          <h1>Dashboard</h1>
        </div>
        <div class="col-sm-6">
          <ol class="float-sm-right">
            <div class="fixed-table-toolbar">
              <div class="bs-bars pull-left">
                <div id="userBulkEditToolbar">
                  <?php echo form_open('dashboard','class="form-inline" id="eventYearForm" ') ?>
                  <div id="users-toolbar">
                    <label for="bulk_actions" class="sr-only">Bulk Actions</label>
                    <select id="filter-year" name="year" class="form-control select2 select2-hidden-accessible" style="min-width:250px;" >
                      <option value="">test</option>
<!--                       <?php //foreach ($dashboard['year_range'] as $row): ?>
                      <option value="<?php //echo $row ?>" <?//= ($dashboard['selected_year'] == $row) ? 'selected' : '' ?>><?php //echo $row ?></option>
                      <?php //endforeach; ?> -->
                    </select>
                    <button class="btn btn-primary" id="bulkUserEditButton">Go</button>
                  </div>
                  <?php echo form_close() ?>
                </div>
              </div>
            </div>
          </ol>
          </div><!-- /.col -->
        </div>
        </div><!-- /.container-fluid -->
      </section>


<!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <!-- row -->
        <!-- Small boxes (Stat box) -->
        


        <div class="row">
          <div class="col-12">

            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Sales Updates</h3>
              </div>
              <!-- /.card-header -->
              <div class="card-body">
                <table id="example1" class="table table-bordered table-striped">
                  <thead>
                  <tr>
                    <th>Rendering engine</th>
                    <th>Browser</th>
                    <th>Platform(s)</th>
                    <th>Engine version</th>
                    <th>CSS grade</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr>
                    <td>Trident</td>
                    <td>Internet
                      Explorer 4.0
                    </td>
                    <td>Win 95+</td>
                    <td> 4</td>
                    <td>X</td>
                  </tr>
                  <tr>
                    <td>Trident</td>
                    <td>Internet
                      Explorer 5.0
                    </td>
                    <td>Win 95+</td>
                    <td>5</td>
                    <td>C</td>
                  </tr>
                  <tr>
                    <td>Trident</td>
                    <td>Internet
                      Explorer 5.5
                    </td>
                    <td>Win 95+</td>
                    <td>5.5</td>
                    <td>A</td>
                  </tr>
                  <tr>
                    <td>Trident</td>
                    <td>Internet
                      Explorer 6
                    </td>
                    <td>Win 98+</td>
                    <td>6</td>
                    <td>A</td>
                  </tr>
                  <tr>
                    <td>Trident</td>
                    <td>Internet Explorer 7</td>
                    <td>Win XP SP2+</td>
                    <td>7</td>
                    <td>A</td>
                  </tr>
                  <tr>
                    <td>Trident</td>
                    <td>AOL browser (AOL desktop)</td>
                    <td>Win XP</td>
                    <td>6</td>
                    <td>A</td>
                  </tr>
                  <tr>
                    <td>Gecko</td>
                    <td>Mozilla 1.1</td>
                    <td>Win 95+ / OSX.1+</td>
                    <td>1.1</td>
                    <td>A</td>
                  </tr>
                  <tr>
                    <td>Gecko</td>
                    <td>Mozilla 1.2</td>
                    <td>Win 95+ / OSX.1+</td>
                    <td>1.2</td>
                    <td>A</td>
                  </tr>
                  </tbody>
                  <tfoot>
                  <tr>
                    <th>Rendering engine</th>
                    <th>Browser</th>
                    <th>Platform(s)</th>
                    <th>Engine version</th>
                    <th>CSS grade</th>
                  </tr>
                  </tfoot>
                </table>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->
          </div>
          <!-- /.col -->
        </div>


        <div class="row">

          <div class="container" style="max-width:1200px">
                  <table id="sale_table" class="display compact nowrap cell-border" style="width:100%">
                    <thead>
                      <tr>
                        <th></th>
                          <!-- <th>Type</th> -->
                          <th>Product</th>
                          <th>Buyer Name</th>
                          <th>Country<br>(Exporting To)</th>
                          <th>Cost</th>
                          <th>Updated<br>Cost</th>
                          <th>Status</th>
                          <th>Updated<br>Status</th>
                          <!-- <th>Date of Sale</th> -->
                          <!-- <th>Date Updated</th> -->
                          <th></th>
                      </tr>
                    </thead>
                    <tbody>
                    </tbody>
                  </table>
                </div>
        </div>





      </div>
    </section>




</div>


<?= $this->endSection(); ?>

<?= $this->section("script") ?>
<!-- DataTables  & Plugins -->
<script src="https://cdn.datatables.net/2.1.3/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/responsive/3.0.2/js/dataTables.responsive.js"></script>
<script src="https://cdn.datatables.net/responsive/3.0.2/js/responsive.dataTables.js"></script>
<script src="<?= base_url("assets/")?>plugins/jquery-validation/jquery.validate.min.js"></script>
<script src="<?= base_url("assets/")?>plugins/inputmask/jquery.inputmask.js"></script>
<script src="<?= base_url("assets/")?>dist/js/sms.js"></script>

<script>

$(document).ready(function() {


});

</script>
<?= $this->endSection() ?>