<?php

namespace App\Controllers;

use App\Controllers\AdminController;
use App\Models\AgencyModel;
use CodeIgniter\HTTP\ResponseInterface;

class AgencyController extends AdminController
{
    protected $agencyModel;

    public function __construct()
    {
        $this->agencyModel = new AgencyModel();
    }

    public function index()
    {
        if (!hasPermission('manage_agencies')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage agencies.');
        }

        $data['agencies'] = $this->agencyModel->findAll();
        $data['title'] = 'Manage Agencies';
        $data['page_title'] = 'Agencies';
        return view('admin/agencies/index', $data);
    }

    public function create()
    {
        if (!hasPermission('manage_agencies')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage agencies.');
        }

        helper(['form']);
        $data = [
            'title'      => 'Create Agency',
            'page_title' => 'Create Agency',
        ];

        if ($this->request->getMethod() == 'post') {
            if (! $this->validate($this->agencyModel->getValidationRules())) {
                $data['validation'] = $this->validator;
            } else {
                $this->agencyModel->save([
                    'name'           => $this->request->getVar('name'),
                    'description'    => $this->request->getVar('description'),
                    'address'        => $this->request->getVar('address'),
                    'contact_person' => $this->request->getVar('contact_person'),
                    'contact_number' => $this->request->getVar('contact_number'),
                    'email'          => $this->request->getVar('email'),
                ]);
                return redirect()->to(base_url('admin/agencies'))->with('success', 'Agency created successfully.');
            }
        }
        return view('admin/agencies/create', $data);
    }

    public function edit($id = null)
    {
        if (!hasPermission('manage_agencies')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage agencies.');
        }

        helper(['form']);
        $agency = $this->agencyModel->find($id);

        if (empty($agency)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Cannot find the agency: ' . $id);
        }

        $data = [
            'agency'     => $agency,
            'title'      => 'Edit Agency',
            'page_title' => 'Edit Agency',
        ];

        if ($this->request->getMethod() == 'post') {
            if (! $this->validate($this->agencyModel->getValidationRules())) {
                $data['validation'] = $this->validator;
            } else {
                $this->agencyModel->update($id, [
                    'name'           => $this->request->getVar('name'),
                    'description'    => $this->request->getVar('description'),
                    'address'        => $this->request->getVar('address'),
                    'contact_person' => $this->request->getVar('contact_person'),
                    'contact_number' => $this->request->getVar('contact_number'),
                    'email'          => $this->request->getVar('email'),
                ]);
                return redirect()->to(base_url('admin/agencies'))->with('success', 'Agency updated successfully.');
            }
        }
        return view('admin/agencies/edit', $data);
    }

    public function delete($id = null)
    {
        if (!hasPermission('manage_agencies')) {
            return redirect()->to(base_url('admin/dashboard'))->with('error', 'You do not have permission to manage agencies.');
        }

        if ($this->agencyModel->delete($id)) {
            return redirect()->to(base_url('admin/agencies'))->with('success', 'Agency deleted successfully.');
        } else {
            return redirect()->to(base_url('admin/agencies'))->with('error', 'Could not delete agency. Make sure no users are assigned to this agency.');
        }
    }
}
