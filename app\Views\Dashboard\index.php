<?= $this->extend('layouts/dashboard'); ?>

<?= $this->section('title'); ?>Dashboard - CARMS<?= $this->endSection(); ?>

<?= $this->section('header'); ?>
<!-- DataTables -->
  <link rel="stylesheet" href="https://cdn.datatables.net/2.1.3/css/dataTables.dataTables.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/3.0.2/css/responsive.dataTables.css">

  <!-- <link rel="stylesheet" href="https://cdn.datatables.net/2.1.8/css/dataTables.dataTables.css"> -->
  <!-- <link rel="stylesheet" href="https://cdn.datatables.net/buttons/3.2.0/css/buttons.dataTables.css"> -->
<style type="text/css">
    .main {
          min-height: 80vh; /* Sets the minimum height to 100% of the viewport height */
        }
</style>
<?= $this->endSection(); ?>

<?= $this->section('content'); ?>

<!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">Dashboard</h1>
          </div><!-- /.col -->
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="#">Home</a></li>
              <li class="breadcrumb-item active">Dashboard</li>
            </ol>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->

    <section class="content">
      <div class="container-fluid">
        <!-- Small boxes (Stat box) -->
        <div class="row">
          <div class="col-lg-3 col-12">
            <!-- small box -->
            <div class="small-box bg-info">
              <div class="inner">
                <h4>Outstanding Receivables</h4>

                <p><?= $statBox['outstanding']; ?></p>
              </div>
              <div class="icon">
                <!-- <i class="ion ion-bag"></i> -->
              </div>
              <!-- <a href="#" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a> -->
            </div>
          </div>
          <!-- ./col -->
         
<!--           <div class="col-lg-3 col-12">
            <div class="small-box bg-success">
              <div class="inner">
                <h4>Current</h4>
                <p><?//= $statBox['current']; ?></p>
              </div>
              <div class="icon">
              </div>
            </div>
          </div> -->
          <!-- ./col -->
          <div class="col-lg-3 col-12">
            <!-- small box -->
            <div class="small-box bg-warning">
              <div class="inner">
                <h4>Current <small>(up to 365 days)</small></h4>

                <p><?= $statBox['current']; ?></p>
              </div>
              <div class="icon">
                <!-- <i class="ion ion-person-add"></i> -->
              </div>
              <!-- <a href="#" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a> -->
            </div>
          </div>
          <!-- ./col -->
           <div class="col-lg-3 col-12">
            <!-- small box -->
            <div class="small-box bg-danger">
              <div class="inner">
                <h4>Past Due <small>(Over 1 year +)</small></h4>

                <p><?= $statBox['pastdue']; ?></p>
              </div>
              <div class="icon">
                <!-- <i class="ion ion-stats-bars"></i> -->
              </div>
              <!-- <a href="#" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a> -->
            </div>
          </div>
          <!-- ./col -->
        </div>
        <!-- /.row -->

    </div>
</section>   



<!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <!-- Aging Summary Table -->
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Aging Receivables Summary</h3>
                                </div>
                                <div class="card-body">
                                    <table id="agingSummaryList" class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Company</th>
                                                <th>Invoice Number</th>
                                                <th>Invoice Date</th>
                                                <!-- <th>Due Date</th> -->
                                                <th>Balance Due</th>
                                                <th>< 90 days</th>
                                                <th>91-365 days</th>
                                                <th>Over 1 year</th>
                                                <th>Over 2 years</th>
                                                <th>Over 3 years +</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Aging Buckets Overview -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Receivables Aging Breakdown</h3>
                                </div>
                                <div class="card-body">
                                    <div id="formattedValues"></div>
                                    <canvas id="agingChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Receivables by Customer -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Outstanding Receivables by Exhibitor</h3>
                                </div>
                                <div class="card-body">
                                    <table id="oustandingReceivableList" class="table">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Company Name</th>
                                                <th>Total Balance</th>
                                                <!-- <th>Last Due Date</th> -->
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Collection Efficiency Metrics -->
<!--                         <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Collection Efficiency Metrics</h3>
                                </div>
                                <div class="card-body">
                                    <p><strong>Average DSO:</strong> 45 Days</p>
                                    <p><strong>% Collected in 30 Days:</strong> 60%</p>
                                    <p><strong>% Collected After 90 Days:</strong> 15%</p>
                                </div>
                            </div>
                        </div> -->
                    </div>
                </div>
            </section>





<?= $this->endSection(); ?>

<?= $this->section("script") ?>
<!-- DataTables  & Plugins -->
<!-- <script src="https://cdn.datatables.net/2.1.3/js/dataTables.js"></script> -->
<script src="https://cdn.datatables.net/responsive/3.0.2/js/dataTables.responsive.js"></script>
<script src="https://cdn.datatables.net/responsive/3.0.2/js/responsive.dataTables.js"></script>
<script src="<?= base_url("assets/")?>plugins/jquery-validation/jquery.validate.min.js"></script>
<script src="<?= base_url("assets/")?>plugins/inputmask/jquery.inputmask.js"></script>
<!-- <script src="<?//= base_url("assets/")?>dist/js/carms.js"></script> -->


<!-- Chart.js Script -->
<!--     <script>
        var ctx = document.getElementById('agingChart').getContext('2d');
        var agingChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['0-30 Days', '31-60 Days', '61-90 Days', '91+ Days'],
                datasets: [{
                    data: [5000, 3000, 1500, 4000],
                    backgroundColor: ['#28a745', '#ffc107', '#17a2b8', '#dc3545']
                }]
            },
            options: {
                responsive: true,
            }
        });
    </script> -->

<!-- Chart.js Script Aging Breakdown ajx -->
    <script>
        var site_url = document.querySelector('meta[name="site-url"]').getAttribute('content');
        var agingChart;

        function fetchAgingData(){
            fetch(site_url+'dashboard/aging-data')
                .then(response=>{
                    if(!response.ok){
                        throw new Error("Network reponse was not ok");
                    }
                    return response.json();
                })
                .then(data=>{
                    if(agingChart){
                        agingChart.destroy();
                    }
                    //create chart here
                    const ctx = document.getElementById('agingChart').getContext('2d');
                    agingChart = new Chart(ctx,{
                        type: 'pie',
                        data: {
                            labels:['below 90 Days', '91-365 Days', 'Over 1 year', 'Over 2 years','over 3 years +'],
                            datasets: [{
                                data: [
                                    parseFloat(data.balance_90_days),
                                    parseFloat(data.balance_91_365_days),
                                    parseFloat(data.balance_over_1_year),
                                    parseFloat(data.balance_over_2_years),
                                    parseFloat(data.balance_over_3_years)
                                        ],
                                backgroundColor: ['#28a745', '#ffc107', '#17a2b8', '#ab4435', '#dc3545']
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'top',
                                },
                                tooltip: {
                                    enabled: true,
                                    callbacks: {
                                        label: function(tooltipItem) {
                                            // let label = tooltipItem.dataset.label||'';
                                        // console.log(label);
                                        let value = tooltipItem.raw;  // Get the raw value of the segment
                                        return formatCurrency(value);  // Apply the currency formatting
                                        }
                                    }
                                }
                            }
                        }
                    });
                    // document.getElementById('formattedValues').innerHTML = `
                    //     0-30 Days: ${formatCurrency(data.zero_to_30)}<br>
                    //     31-60 Days: ${formatCurrency(data.thirty_to_60)}<br>
                    //     61-90 Days: ${formatCurrency(data.sixty_to_90)}<br>
                    //     90+ Days: ${formatCurrency(data.ninety_plus)}
                    // `;

                }).catch(error => {
                    console.log("Error fetching aging data.");
                });
        }

        function formatNumber(num) {
            return num.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }

        function formatCurrency(num) {
            return ' '+ new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(num);
        }

        document.addEventListener('DOMContentLoaded',function(){
            fetchAgingData();
        });

        
    </script>

<script>

$(document).ready(function() {
    var agingSummaryTable = $('#agingSummaryList').DataTable({
      paging: true,
      ordering: false,
      // info: false,
      lengthChange: false,
      lengthMenu: [5,10, 25, 50, 75, 100],
      // searching : true,
      // dom: '<"top"B>frt<"bottom"lip><"clear">',
      // dom: 'Bfrtip',
      // responsive: true,
      // lengthChange: true,
      // autoWidth: false,
      search: { return: true },
      pagingType: 'simple_numbers',  // Options: 'simple', 'simple_numbers', 'full', 'full_numbers'
      processing: true,
      serverSide: true,
      ajax:{
        url: site_url+'dashboard/aging-summary-data',
        // data: function(d){
        //   d.status = $('#filter-status').val();
        // },
      },
      // columnDefs: [{
      //   targets: [],
      //   visible: false,
      // }],
      columnDefs: [
        {
          className: 'dt-head-center', 
          targets: '_all',
      }],
      // order:[[1, 'asc']],
      columns: [
        // { data: '#', orderable: false},
        { data: 'co_name' },
          { data: 'invoice_no' },
          { data: 'invoice_date' , searchable:false},
          // { data: 'due_date' },
          // { data: 'net_receivable_amount' },
          { data: 'balance_due',"className": "dt-body-right", searchable:false },
          { data: 'balance_90_days',"className": "dt-body-right", searchable:false },
          { data: 'balance_91_365_days',"className": "dt-body-right", searchable:false },
          { data: 'balance_over_1_year',"className": "dt-body-right", searchable:false },
          { data: 'balance_over_2_years',"className": "dt-body-right", searchable:false },
          { data: 'balance_over_3_years',"className": "dt-body-right", searchable:false },
      ],
    });


    var outstandingReceivableTable = $('#oustandingReceivableList').DataTable({
        info: false,
      paging: false,
      ordering: false,
      lengthChange: false,
      searching : false,
      processing: true,
      serverSide: true,
      ajax:{
        url: site_url+'dashboard/outstanding-receivable-data',
      },
      order:[[1, 'asc']],
      columns: [
        { data: '#', orderable: false,searchable:false},
        { data: 'co_name' },
          { data: 'outstanding_amount' },
          // { data: 'last_due_date' },

      ],
    });




});

</script>
<?= $this->endSection() ?>
